"""
Subscription Abuse Detection and Prevention System
"""

import time
import logging
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from django.core.cache import cache
from django.utils import timezone
from django.conf import settings

logger = logging.getLogger(__name__)


class SubscriptionAbuseDetector:
    """
    Advanced abuse detection for newsletter subscriptions
    """
    
    def __init__(self):
        self.cache_timeout = getattr(settings, 'ABUSE_DETECTION_CACHE_TIMEOUT', 3600)
        self.max_ip_subscriptions_per_hour = 5
        self.max_ip_subscriptions_per_day = 20
        self.suspicious_threshold = 10
        
    def check_subscription_abuse(self, email: str, ip_address: str, user_agent: str = None) -> Dict[str, any]:
        """
        Comprehensive abuse detection for subscription attempts
        
        Returns:
            Dict with abuse detection results and recommended actions
        """
        checks = {
            'ip_rate_limit': self._check_ip_rate_limits(ip_address),
            'email_frequency': self._check_email_frequency(email),
            'suspicious_patterns': self._check_suspicious_patterns(email, ip_address, user_agent),
            'bot_detection': self._check_bot_patterns(user_agent, ip_address),
            'geographic_anomaly': self._check_geographic_patterns(ip_address),
            'timing_analysis': self._check_timing_patterns(ip_address)
        }
        
        # Calculate abuse score
        abuse_score = self._calculate_abuse_score(checks)
        
        # Determine action
        action = self._determine_action(abuse_score, checks)
        
        # Log suspicious activity
        if abuse_score >= 60:
            self._log_suspicious_activity(email, ip_address, user_agent, checks, abuse_score)
        
        return {
            'abuse_detected': abuse_score >= 70,
            'abuse_score': abuse_score,
            'checks': checks,
            'action': action,
            'block_subscription': action in ['block', 'captcha_required'],
            'require_captcha': action == 'captcha_required'
        }
    
    def _check_ip_rate_limits(self, ip_address: str) -> Dict[str, any]:
        """
        Check IP-based rate limits
        """
        # Hourly limit
        hourly_key = f"ip_subs_hour_{ip_address}_{datetime.now().strftime('%Y%m%d%H')}"
        hourly_count = cache.get(hourly_key, 0)
        
        # Daily limit
        daily_key = f"ip_subs_day_{ip_address}_{datetime.now().strftime('%Y%m%d')}"
        daily_count = cache.get(daily_key, 0)
        
        # Update counters
        cache.set(hourly_key, hourly_count + 1, 3600)
        cache.set(daily_key, daily_count + 1, 86400)
        
        exceeded_hourly = hourly_count >= self.max_ip_subscriptions_per_hour
        exceeded_daily = daily_count >= self.max_ip_subscriptions_per_day
        
        return {
            'exceeded_hourly': exceeded_hourly,
            'exceeded_daily': exceeded_daily,
            'hourly_count': hourly_count + 1,
            'daily_count': daily_count + 1,
            'severity': 'high' if exceeded_hourly or exceeded_daily else 'low'
        }
    
    def _check_email_frequency(self, email: str) -> Dict[str, any]:
        """
        Check email subscription frequency patterns
        """
        email_hash = hashlib.md5(email.encode()).hexdigest()
        
        # Check recent attempts
        recent_key = f"email_recent_{email_hash}"
        recent_attempts = cache.get(recent_key, [])
        
        now = time.time()
        # Filter attempts from last hour
        recent_attempts = [t for t in recent_attempts if now - t < 3600]
        recent_attempts.append(now)
        
        cache.set(recent_key, recent_attempts, 3600)
        
        # Check for rapid succession
        rapid_succession = len(recent_attempts) >= 3
        
        # Check time between attempts
        if len(recent_attempts) >= 2:
            time_diff = recent_attempts[-1] - recent_attempts[-2]
            too_fast = time_diff < 60  # Less than 1 minute
        else:
            too_fast = False
        
        return {
            'rapid_succession': rapid_succession,
            'too_fast': too_fast,
            'recent_count': len(recent_attempts),
            'severity': 'high' if rapid_succession or too_fast else 'low'
        }
    
    def _check_suspicious_patterns(self, email: str, ip_address: str, user_agent: str) -> Dict[str, any]:
        """
        Check for suspicious patterns in subscription data
        """
        suspicious_indicators = []
        
        # Email patterns
        if '+test' in email or '+spam' in email:
            suspicious_indicators.append('test_email_pattern')
        
        # Sequential email patterns
        if self._is_sequential_email(email):
            suspicious_indicators.append('sequential_email')
        
        # User agent patterns
        if user_agent:
            if 'bot' in user_agent.lower() or 'crawler' in user_agent.lower():
                suspicious_indicators.append('bot_user_agent')
            
            if len(user_agent) < 20:  # Very short user agent
                suspicious_indicators.append('short_user_agent')
        
        # IP patterns
        if self._is_suspicious_ip(ip_address):
            suspicious_indicators.append('suspicious_ip')
        
        return {
            'indicators': suspicious_indicators,
            'count': len(suspicious_indicators),
            'severity': 'high' if len(suspicious_indicators) >= 2 else 'medium' if suspicious_indicators else 'low'
        }
    
    def _check_bot_patterns(self, user_agent: str, ip_address: str) -> Dict[str, any]:
        """
        Detect bot-like behavior patterns
        """
        bot_indicators = []
        
        if not user_agent:
            bot_indicators.append('missing_user_agent')
        elif user_agent:
            # Common bot patterns
            bot_patterns = [
                'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget',
                'python-requests', 'http', 'automated'
            ]
            
            for pattern in bot_patterns:
                if pattern in user_agent.lower():
                    bot_indicators.append(f'bot_pattern_{pattern}')
                    break
            
            # Suspicious user agent characteristics
            if len(user_agent) > 500:  # Unusually long
                bot_indicators.append('long_user_agent')
            
            if user_agent.count('(') != user_agent.count(')'):  # Malformed
                bot_indicators.append('malformed_user_agent')
        
        # Check for automated timing patterns
        timing_key = f"bot_timing_{ip_address}"
        request_times = cache.get(timing_key, [])
        
        now = time.time()
        request_times = [t for t in request_times if now - t < 300]  # Last 5 minutes
        request_times.append(now)
        
        cache.set(timing_key, request_times, 300)
        
        # Check for too regular timing (bot-like)
        if len(request_times) >= 3:
            intervals = [request_times[i] - request_times[i-1] for i in range(1, len(request_times))]
            avg_interval = sum(intervals) / len(intervals)
            
            # Very regular intervals suggest automation
            if all(abs(interval - avg_interval) < 1 for interval in intervals):
                bot_indicators.append('regular_timing_pattern')
        
        return {
            'indicators': bot_indicators,
            'count': len(bot_indicators),
            'severity': 'high' if len(bot_indicators) >= 2 else 'medium' if bot_indicators else 'low'
        }
    
    def _check_geographic_patterns(self, ip_address: str) -> Dict[str, any]:
        """
        Check for geographic anomalies (placeholder for future enhancement)
        """
        # This could be enhanced with IP geolocation services
        # For now, just basic checks
        
        # Check for known VPN/proxy IP ranges (simplified)
        suspicious_ranges = [
            '10.', '192.168.', '172.16.', '127.'  # Private ranges
        ]
        
        is_private = any(ip_address.startswith(range_) for range_ in suspicious_ranges)
        
        return {
            'is_private_ip': is_private,
            'severity': 'medium' if is_private else 'low'
        }
    
    def _check_timing_patterns(self, ip_address: str) -> Dict[str, any]:
        """
        Analyze timing patterns for abuse detection
        """
        timing_key = f"timing_pattern_{ip_address}"
        timestamps = cache.get(timing_key, [])
        
        now = time.time()
        # Keep last 24 hours of timestamps
        timestamps = [t for t in timestamps if now - t < 86400]
        timestamps.append(now)
        
        cache.set(timing_key, timestamps, 86400)
        
        # Analyze patterns
        if len(timestamps) >= 5:
            # Check for burst patterns
            recent_burst = sum(1 for t in timestamps if now - t < 300) >= 3  # 3 in 5 minutes
            
            # Check for regular intervals (automation)
            if len(timestamps) >= 3:
                intervals = [timestamps[i] - timestamps[i-1] for i in range(1, len(timestamps))]
                avg_interval = sum(intervals) / len(intervals)
                regular_pattern = all(abs(interval - avg_interval) < 5 for interval in intervals[-3:])
            else:
                regular_pattern = False
        else:
            recent_burst = False
            regular_pattern = False
        
        return {
            'recent_burst': recent_burst,
            'regular_pattern': regular_pattern,
            'total_requests': len(timestamps),
            'severity': 'high' if recent_burst or regular_pattern else 'low'
        }
    
    def _is_sequential_email(self, email: str) -> bool:
        """
        Check if email follows sequential patterns (e.g., test1@, test2@, etc.)
        """
        import re
        pattern = r'^[a-zA-Z]+\d+@'
        return bool(re.match(pattern, email))
    
    def _is_suspicious_ip(self, ip_address: str) -> bool:
        """
        Check if IP is in suspicious ranges or known bad actors
        """
        # This could be enhanced with threat intelligence feeds
        # For now, basic checks
        
        # Check against cached suspicious IPs
        suspicious_key = f"suspicious_ip_{hashlib.md5(ip_address.encode()).hexdigest()}"
        return cache.get(suspicious_key, False)
    
    def _calculate_abuse_score(self, checks: Dict) -> int:
        """
        Calculate overall abuse score (0-100)
        """
        score = 0
        
        # IP rate limits
        if checks['ip_rate_limit']['exceeded_hourly']:
            score += 40
        if checks['ip_rate_limit']['exceeded_daily']:
            score += 30
        
        # Email frequency
        if checks['email_frequency']['rapid_succession']:
            score += 35
        if checks['email_frequency']['too_fast']:
            score += 25
        
        # Suspicious patterns
        score += checks['suspicious_patterns']['count'] * 15
        
        # Bot detection
        score += checks['bot_detection']['count'] * 20
        
        # Geographic anomalies
        if checks['geographic_anomaly']['is_private_ip']:
            score += 10
        
        # Timing patterns
        if checks['timing_analysis']['recent_burst']:
            score += 30
        if checks['timing_analysis']['regular_pattern']:
            score += 25
        
        return min(score, 100)
    
    def _determine_action(self, abuse_score: int, checks: Dict) -> str:
        """
        Determine appropriate action based on abuse score
        """
        if abuse_score >= 80:
            return 'block'
        elif abuse_score >= 60:
            return 'captcha_required'
        elif abuse_score >= 40:
            return 'monitor'
        else:
            return 'allow'
    
    def _log_suspicious_activity(self, email: str, ip_address: str, user_agent: str, 
                               checks: Dict, abuse_score: int):
        """
        Log suspicious subscription activity
        """
        logger.warning(
            f"Suspicious subscription activity detected: "
            f"Email: {email}, IP: {ip_address}, Score: {abuse_score}, "
            f"User-Agent: {user_agent}, Checks: {checks}"
        )
        
        # Store in cache for admin review
        incident_key = f"abuse_incident_{int(time.time())}"
        incident_data = {
            'timestamp': timezone.now().isoformat(),
            'email': email,
            'ip_address': ip_address,
            'user_agent': user_agent,
            'abuse_score': abuse_score,
            'checks': checks
        }
        cache.set(incident_key, incident_data, 86400)  # Keep for 24 hours


# Global instance
subscription_abuse_detector = SubscriptionAbuseDetector()
