"""
Security Monitoring and Logging System for Newsletter Subscriptions
Provides comprehensive security event tracking and alerting
"""

import time
import json
import logging
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from django.core.cache import cache
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
from django.db import models

logger = logging.getLogger(__name__)


class SecurityEventType:
    """Security event type constants"""
    RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded'
    EMAIL_SECURITY_BLOCK = 'email_security_block'
    ABUSE_DETECTED = 'abuse_detected'
    BOT_DETECTED = 'bot_detected'
    SUSPICIOUS_PATTERN = 'suspicious_pattern'
    IP_BLOCKED = 'ip_blocked'
    DISPOSABLE_EMAIL = 'disposable_email'
    RAPID_SUCCESSION = 'rapid_succession'
    HONEYPOT_TRIGGERED = 'honeypot_triggered'
    SUBSCRIPTION_SUCCESS = 'subscription_success'
    INVALID_EMAIL = 'invalid_email'
    INVALID_INPUT = 'invalid_input'
    STOCK_ALERT_CREATED = 'stock_alert_created'


class SecurityMonitor:
    """
    Comprehensive security monitoring and alerting system
    """
    
    def __init__(self):
        self.cache_timeout = getattr(settings, 'SECURITY_MONITOR_CACHE_TIMEOUT', 86400)
        self.alert_threshold = getattr(settings, 'SECURITY_ALERT_THRESHOLD', 10)
        self.admin_emails = getattr(settings, 'SECURITY_ADMIN_EMAILS', ['<EMAIL>'])
        
    def log_security_event(self, event_type: str, email: str, ip_address: str, 
                          details: Dict[str, Any], severity: str = 'medium'):
        """
        Log a security event with comprehensive details
        """
        event_data = {
            'timestamp': timezone.now().isoformat(),
            'event_type': event_type,
            'email': email,
            'ip_address': ip_address,
            'severity': severity,
            'details': details,
            'user_agent': details.get('user_agent', ''),
            'request_data': details.get('request_data', {}),
            'security_scores': details.get('security_scores', {}),
            'geolocation': self._get_ip_geolocation(ip_address)
        }
        
        # Log to Django logger
        log_message = (
            f"Security Event [{event_type}] - "
            f"Email: {email}, IP: {ip_address}, "
            f"Severity: {severity}, Details: {json.dumps(details)}"
        )
        
        if severity == 'high':
            logger.error(log_message)
        elif severity == 'medium':
            logger.warning(log_message)
        else:
            logger.info(log_message)
        
        # Store in cache for dashboard
        event_key = f"security_event_{int(time.time())}_{hashlib.md5(f'{email}{ip_address}'.encode()).hexdigest()[:8]}"
        cache.set(event_key, event_data, self.cache_timeout)
        
        # Update security metrics
        self._update_security_metrics(event_type, ip_address, severity)
        
        # Check if alert should be sent
        self._check_alert_conditions(event_type, ip_address, severity)
        
        return event_key
    
    def get_security_dashboard_data(self, hours: int = 24) -> Dict[str, Any]:
        """
        Get security dashboard data for the specified time period
        """
        # Get all security events from cache
        all_keys = cache.keys('security_event_*') if hasattr(cache, 'keys') else []
        events = []
        
        cutoff_time = timezone.now() - timedelta(hours=hours)
        
        for key in all_keys:
            event_data = cache.get(key)
            if event_data:
                event_time = datetime.fromisoformat(event_data['timestamp'].replace('Z', '+00:00'))
                if event_time >= cutoff_time:
                    events.append(event_data)
        
        # Analyze events
        analysis = self._analyze_security_events(events)
        
        return {
            'total_events': len(events),
            'time_period_hours': hours,
            'events_by_type': analysis['events_by_type'],
            'events_by_severity': analysis['events_by_severity'],
            'top_blocked_ips': analysis['top_blocked_ips'],
            'top_blocked_emails': analysis['top_blocked_emails'],
            'hourly_distribution': analysis['hourly_distribution'],
            'recent_events': sorted(events, key=lambda x: x['timestamp'], reverse=True)[:20],
            'security_trends': analysis['security_trends'],
            'recommendations': self._generate_security_recommendations(analysis)
        }
    
    def get_ip_security_status(self, ip_address: str) -> Dict[str, Any]:
        """
        Get comprehensive security status for an IP address
        """
        ip_hash = hashlib.md5(ip_address.encode()).hexdigest()
        
        # Get recent events for this IP
        recent_events = []
        all_keys = cache.keys('security_event_*') if hasattr(cache, 'keys') else []
        
        for key in all_keys:
            event_data = cache.get(key)
            if event_data and event_data['ip_address'] == ip_address:
                recent_events.append(event_data)
        
        # Check various security flags
        blocked_key = f"newsletter_blocked_{ip_address}"
        is_blocked = cache.get(blocked_key, False)
        
        penalty_key = f"newsletter_penalty_{ip_address}"
        penalty_count = cache.get(penalty_key, 0)
        
        suspicious_key = f"suspicious_ip_{ip_hash}"
        is_suspicious = cache.get(suspicious_key, False)
        
        # Calculate risk score
        risk_score = self._calculate_ip_risk_score(recent_events, penalty_count, is_suspicious)
        
        return {
            'ip_address': ip_address,
            'is_blocked': is_blocked,
            'is_suspicious': is_suspicious,
            'penalty_count': penalty_count,
            'risk_score': risk_score,
            'risk_level': self._get_risk_level(risk_score),
            'recent_events_count': len(recent_events),
            'recent_events': sorted(recent_events, key=lambda x: x['timestamp'], reverse=True)[:10],
            'last_activity': recent_events[0]['timestamp'] if recent_events else None,
            'geolocation': self._get_ip_geolocation(ip_address),
            'recommendations': self._get_ip_recommendations(risk_score, recent_events)
        }
    
    def block_ip_address(self, ip_address: str, duration_seconds: int = 3600, reason: str = 'Manual block'):
        """
        Manually block an IP address
        """
        block_key = f"newsletter_blocked_{ip_address}"
        cache.set(block_key, True, duration_seconds)
        
        # Log the manual block
        self.log_security_event(
            SecurityEventType.IP_BLOCKED,
            '',
            ip_address,
            {
                'reason': reason,
                'duration_seconds': duration_seconds,
                'manual_block': True
            },
            'high'
        )
        
        logger.warning(f"IP {ip_address} manually blocked for {duration_seconds}s. Reason: {reason}")
    
    def unblock_ip_address(self, ip_address: str, reason: str = 'Manual unblock'):
        """
        Manually unblock an IP address
        """
        block_key = f"newsletter_blocked_{ip_address}"
        cache.delete(block_key)
        
        # Reset penalty counter
        penalty_key = f"newsletter_penalty_{ip_address}"
        cache.delete(penalty_key)
        
        logger.info(f"IP {ip_address} manually unblocked. Reason: {reason}")
    
    def _update_security_metrics(self, event_type: str, ip_address: str, severity: str):
        """
        Update security metrics in cache
        """
        # Update hourly metrics
        hour_key = f"security_metrics_hour_{datetime.now().strftime('%Y%m%d%H')}"
        hourly_metrics = cache.get(hour_key, {
            'total_events': 0,
            'events_by_type': {},
            'events_by_severity': {},
            'unique_ips': set()
        })
        
        hourly_metrics['total_events'] += 1
        hourly_metrics['events_by_type'][event_type] = hourly_metrics['events_by_type'].get(event_type, 0) + 1
        hourly_metrics['events_by_severity'][severity] = hourly_metrics['events_by_severity'].get(severity, 0) + 1
        hourly_metrics['unique_ips'].add(ip_address)
        
        cache.set(hour_key, hourly_metrics, 3600)
        
        # Update daily metrics
        day_key = f"security_metrics_day_{datetime.now().strftime('%Y%m%d')}"
        daily_metrics = cache.get(day_key, {
            'total_events': 0,
            'events_by_type': {},
            'events_by_severity': {},
            'unique_ips': set()
        })
        
        daily_metrics['total_events'] += 1
        daily_metrics['events_by_type'][event_type] = daily_metrics['events_by_type'].get(event_type, 0) + 1
        daily_metrics['events_by_severity'][severity] = daily_metrics['events_by_severity'].get(severity, 0) + 1
        daily_metrics['unique_ips'].add(ip_address)
        
        cache.set(day_key, daily_metrics, 86400)
    
    def _check_alert_conditions(self, event_type: str, ip_address: str, severity: str):
        """
        Check if security alerts should be sent
        """
        # Check for high-severity events
        if severity == 'high':
            self._send_security_alert(
                f"High-severity security event: {event_type}",
                f"IP {ip_address} triggered a high-severity security event of type {event_type}"
            )
        
        # Check for event frequency thresholds
        hour_key = f"security_metrics_hour_{datetime.now().strftime('%Y%m%d%H')}"
        hourly_metrics = cache.get(hour_key, {})
        
        if hourly_metrics.get('total_events', 0) >= self.alert_threshold:
            self._send_security_alert(
                "Security event threshold exceeded",
                f"More than {self.alert_threshold} security events in the last hour"
            )
    
    def _send_security_alert(self, subject: str, message: str):
        """
        Send security alert email to administrators
        """
        try:
            send_mail(
                subject=f"[Security Alert] {subject}",
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=self.admin_emails,
                fail_silently=False
            )
            logger.info(f"Security alert sent: {subject}")
        except Exception as e:
            logger.error(f"Failed to send security alert: {str(e)}")
    
    def _analyze_security_events(self, events: List[Dict]) -> Dict[str, Any]:
        """
        Analyze security events for dashboard
        """
        events_by_type = {}
        events_by_severity = {}
        ip_counts = {}
        email_counts = {}
        hourly_distribution = {}
        
        for event in events:
            # Count by type
            event_type = event['event_type']
            events_by_type[event_type] = events_by_type.get(event_type, 0) + 1
            
            # Count by severity
            severity = event['severity']
            events_by_severity[severity] = events_by_severity.get(severity, 0) + 1
            
            # Count by IP
            ip = event['ip_address']
            ip_counts[ip] = ip_counts.get(ip, 0) + 1
            
            # Count by email
            email = event['email']
            if email:
                email_counts[email] = email_counts.get(email, 0) + 1
            
            # Hourly distribution
            hour = datetime.fromisoformat(event['timestamp'].replace('Z', '+00:00')).hour
            hourly_distribution[hour] = hourly_distribution.get(hour, 0) + 1
        
        # Get top offenders
        top_blocked_ips = sorted(ip_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        top_blocked_emails = sorted(email_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        
        # Calculate trends
        security_trends = self._calculate_security_trends(events)
        
        return {
            'events_by_type': events_by_type,
            'events_by_severity': events_by_severity,
            'top_blocked_ips': top_blocked_ips,
            'top_blocked_emails': top_blocked_emails,
            'hourly_distribution': hourly_distribution,
            'security_trends': security_trends
        }
    
    def _calculate_security_trends(self, events: List[Dict]) -> Dict[str, Any]:
        """
        Calculate security trends from events
        """
        if len(events) < 2:
            return {'trend': 'insufficient_data'}
        
        # Sort events by timestamp
        sorted_events = sorted(events, key=lambda x: x['timestamp'])
        
        # Calculate hourly event rates
        hourly_rates = {}
        for event in sorted_events:
            hour = datetime.fromisoformat(event['timestamp'].replace('Z', '+00:00')).strftime('%Y%m%d%H')
            hourly_rates[hour] = hourly_rates.get(hour, 0) + 1
        
        # Calculate trend
        hours = sorted(hourly_rates.keys())
        if len(hours) >= 2:
            recent_rate = sum(hourly_rates[h] for h in hours[-3:]) / min(3, len(hours[-3:]))
            earlier_rate = sum(hourly_rates[h] for h in hours[:-3]) / max(1, len(hours[:-3]))
            
            if recent_rate > earlier_rate * 1.5:
                trend = 'increasing'
            elif recent_rate < earlier_rate * 0.5:
                trend = 'decreasing'
            else:
                trend = 'stable'
        else:
            trend = 'stable'
        
        return {
            'trend': trend,
            'recent_rate': recent_rate if 'recent_rate' in locals() else 0,
            'earlier_rate': earlier_rate if 'earlier_rate' in locals() else 0
        }
    
    def _calculate_ip_risk_score(self, events: List[Dict], penalty_count: int, is_suspicious: bool) -> int:
        """
        Calculate risk score for an IP address
        """
        score = 0
        
        # Base score from events
        score += len(events) * 10
        
        # Penalty multiplier
        score += penalty_count * 20
        
        # Suspicious flag
        if is_suspicious:
            score += 30
        
        # Recent high-severity events
        recent_high_severity = sum(1 for e in events if e['severity'] == 'high')
        score += recent_high_severity * 25
        
        return min(score, 100)
    
    def _get_risk_level(self, score: int) -> str:
        """Convert risk score to risk level"""
        if score >= 80:
            return 'critical'
        elif score >= 60:
            return 'high'
        elif score >= 40:
            return 'medium'
        elif score >= 20:
            return 'low'
        else:
            return 'minimal'
    
    def _get_ip_recommendations(self, risk_score: int, events: List[Dict]) -> List[str]:
        """Get security recommendations for an IP"""
        recommendations = []
        
        if risk_score >= 80:
            recommendations.append('Consider permanent IP blocking')
        elif risk_score >= 60:
            recommendations.append('Implement extended rate limiting')
        elif risk_score >= 40:
            recommendations.append('Monitor closely for additional suspicious activity')
        
        if len(events) >= 5:
            recommendations.append('Review event patterns for automation indicators')
        
        return recommendations
    
    def _generate_security_recommendations(self, analysis: Dict) -> List[str]:
        """Generate security recommendations based on analysis"""
        recommendations = []
        
        total_events = sum(analysis['events_by_severity'].values())
        high_severity = analysis['events_by_severity'].get('high', 0)
        
        if high_severity > total_events * 0.3:
            recommendations.append('High percentage of severe security events - review security policies')
        
        if analysis['events_by_type'].get(SecurityEventType.BOT_DETECTED, 0) > 10:
            recommendations.append('Consider implementing CAPTCHA for newsletter subscriptions')
        
        if len(analysis['top_blocked_ips']) > 5:
            recommendations.append('Multiple IPs showing suspicious behavior - possible coordinated attack')
        
        return recommendations
    
    def _get_ip_geolocation(self, ip_address: str) -> Dict[str, str]:
        """Get IP geolocation (placeholder for future enhancement)"""
        # This could be enhanced with IP geolocation services
        return {
            'country': 'Unknown',
            'city': 'Unknown',
            'region': 'Unknown'
        }


# Global instance
security_monitor = SecurityMonitor()
