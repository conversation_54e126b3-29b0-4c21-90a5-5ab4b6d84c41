"""
Email Security Module for Newsletter Subscriptions
Provides comprehensive email validation and security checks
"""

import re
import logging
import hashlib
from typing import Dict, List, Tuple, Optional
from django.core.cache import cache
from django.conf import settings

logger = logging.getLogger(__name__)


class EmailSecurityValidator:
    """
    Comprehensive email security validation system
    """
    
    # Known disposable email domains (sample list - should be expanded)
    DISPOSABLE_DOMAINS = {
        '10minutemail.com', 'tempmail.org', 'guerrillamail.com', 
        'mailinator.com', 'yopmail.com', 'temp-mail.org',
        'throwaway.email', 'getnada.com', 'maildrop.cc',
        'sharklasers.com', 'grr.la', 'guerrillamailblock.com'
    }
    
    # Suspicious email patterns
    SUSPICIOUS_PATTERNS = [
        r'test\d*@test',
        r'spam\d*@spam',
        r'bot\d*@bot',
        r'fake\d*@fake',
        r'temp\d*@temp',
        r'admin@admin',
        r'user@user',
        r'email@email',
        r'sample@sample',
        r'example@example',
        r'noreply@noreply',
        r'[a-z]{1,3}\d{3,}@[a-z]{1,3}\d{3,}',  # Short random patterns
        r'[a-z]+\+[a-z]+\+[a-z]+@',  # Multiple plus signs
        r'[0-9]{8,}@',  # Long numeric usernames
    ]
    
    # High-risk TLDs
    HIGH_RISK_TLDS = {
        '.tk', '.ml', '.ga', '.cf', '.gq', '.top', '.click',
        '.download', '.stream', '.science', '.racing'
    }
    
    def __init__(self):
        self.cache_timeout = getattr(settings, 'EMAIL_SECURITY_CACHE_TIMEOUT', 3600)
    
    def validate_email_security(self, email: str, ip_address: str = None) -> Dict[str, any]:
        """
        Comprehensive email security validation
        
        Returns:
            Dict with validation results and security flags
        """
        if not email:
            return {
                'valid': False,
                'reason': 'Email is required',
                'security_risk': 'high',
                'block_subscription': True
            }
        
        email = email.strip().lower()
        
        # Basic format validation
        if not self._is_valid_email_format(email):
            return {
                'valid': False,
                'reason': 'Invalid email format',
                'security_risk': 'medium',
                'block_subscription': True
            }
        
        # Security checks
        security_checks = {
            'disposable': self._check_disposable_email(email),
            'suspicious_pattern': self._check_suspicious_patterns(email),
            'high_risk_domain': self._check_high_risk_domain(email),
            'rate_limit_exceeded': self._check_email_rate_limit(email),
            'domain_reputation': self._check_domain_reputation(email)
        }
        
        # Calculate risk score
        risk_score = self._calculate_risk_score(security_checks)
        
        # Determine if subscription should be blocked
        block_subscription = risk_score >= 70 or any([
            security_checks['disposable']['detected'],
            security_checks['suspicious_pattern']['detected'],
            security_checks['rate_limit_exceeded']['detected']
        ])
        
        # Log security events
        if block_subscription and ip_address:
            logger.warning(
                f"Email security block: {email} from IP {ip_address}. "
                f"Risk score: {risk_score}. Checks: {security_checks}"
            )
        
        return {
            'valid': not block_subscription,
            'email': email,
            'risk_score': risk_score,
            'security_checks': security_checks,
            'security_risk': self._get_risk_level(risk_score),
            'block_subscription': block_subscription,
            'reason': self._get_block_reason(security_checks) if block_subscription else None
        }
    
    def _is_valid_email_format(self, email: str) -> bool:
        """
        Enhanced email format validation
        """
        # RFC 5322 compliant regex (simplified)
        pattern = r'^[a-zA-Z0-9.!#$%&\'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$'
        
        if not re.match(pattern, email):
            return False
        
        # Additional checks
        local, domain = email.split('@', 1)
        
        # Check local part length
        if len(local) > 64 or len(local) < 1:
            return False
        
        # Check domain part length
        if len(domain) > 253 or len(domain) < 1:
            return False
        
        # Check for consecutive dots
        if '..' in email:
            return False
        
        # Check for valid TLD
        if '.' not in domain:
            return False
        
        return True
    
    def _check_disposable_email(self, email: str) -> Dict[str, any]:
        """
        Check if email uses a disposable email service
        """
        domain = email.split('@')[1]
        
        # Check against known disposable domains
        is_disposable = domain in self.DISPOSABLE_DOMAINS
        
        # Check cache for previously identified disposable domains
        cache_key = f"disposable_domain_{hashlib.md5(domain.encode()).hexdigest()}"
        cached_result = cache.get(cache_key)
        
        if cached_result is not None:
            is_disposable = cached_result
        
        return {
            'detected': is_disposable,
            'domain': domain,
            'confidence': 'high' if is_disposable else 'low'
        }
    
    def _check_suspicious_patterns(self, email: str) -> Dict[str, any]:
        """
        Check for suspicious email patterns
        """
        for pattern in self.SUSPICIOUS_PATTERNS:
            if re.search(pattern, email, re.IGNORECASE):
                return {
                    'detected': True,
                    'pattern': pattern,
                    'confidence': 'high'
                }
        
        return {
            'detected': False,
            'pattern': None,
            'confidence': 'low'
        }
    
    def _check_high_risk_domain(self, email: str) -> Dict[str, any]:
        """
        Check if email domain uses high-risk TLD
        """
        domain = email.split('@')[1]
        
        for tld in self.HIGH_RISK_TLDS:
            if domain.endswith(tld):
                return {
                    'detected': True,
                    'tld': tld,
                    'confidence': 'medium'
                }
        
        return {
            'detected': False,
            'tld': None,
            'confidence': 'low'
        }
    
    def _check_email_rate_limit(self, email: str) -> Dict[str, any]:
        """
        Check if email has exceeded subscription rate limits
        """
        email_hash = hashlib.md5(email.encode()).hexdigest()
        cache_key = f"email_attempts_{email_hash}"
        
        attempts = cache.get(cache_key, 0)
        max_attempts = 3  # Max 3 attempts per hour per email
        
        if attempts >= max_attempts:
            return {
                'detected': True,
                'attempts': attempts,
                'confidence': 'high'
            }
        
        # Increment counter
        cache.set(cache_key, attempts + 1, 3600)  # 1 hour timeout
        
        return {
            'detected': False,
            'attempts': attempts + 1,
            'confidence': 'low'
        }
    
    def _check_domain_reputation(self, email: str) -> Dict[str, any]:
        """
        Check domain reputation (placeholder for future enhancement)
        """
        domain = email.split('@')[1]
        
        # This could be enhanced with external reputation services
        # For now, just check against common legitimate domains
        trusted_domains = {
            'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
            'icloud.com', 'protonmail.com', 'aol.com'
        }
        
        is_trusted = domain in trusted_domains
        
        return {
            'trusted': is_trusted,
            'domain': domain,
            'confidence': 'medium' if is_trusted else 'low'
        }
    
    def _calculate_risk_score(self, checks: Dict) -> int:
        """
        Calculate overall risk score (0-100)
        """
        score = 0
        
        if checks['disposable']['detected']:
            score += 80
        
        if checks['suspicious_pattern']['detected']:
            score += 70
        
        if checks['high_risk_domain']['detected']:
            score += 30
        
        if checks['rate_limit_exceeded']['detected']:
            score += 60
        
        if not checks['domain_reputation']['trusted']:
            score += 10
        
        return min(score, 100)
    
    def _get_risk_level(self, score: int) -> str:
        """
        Convert risk score to risk level
        """
        if score >= 70:
            return 'high'
        elif score >= 40:
            return 'medium'
        else:
            return 'low'
    
    def _get_block_reason(self, checks: Dict) -> str:
        """
        Get human-readable reason for blocking
        """
        if checks['disposable']['detected']:
            return 'Disposable email addresses are not allowed'
        
        if checks['suspicious_pattern']['detected']:
            return 'Email address contains suspicious patterns'
        
        if checks['rate_limit_exceeded']['detected']:
            return 'Too many subscription attempts with this email'
        
        if checks['high_risk_domain']['detected']:
            return 'Email domain is considered high-risk'
        
        return 'Email failed security validation'


# Global instance
email_security_validator = EmailSecurityValidator()
