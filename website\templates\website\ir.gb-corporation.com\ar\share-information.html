{% extends "website/ir.gb-corporation.com/base.html" %}

{% block extra_head %}
<!-- Include both headers -->
{% include "website/ir.gb-corporation.com/includes/header-ar.html" %}


<!-- Styles to handle multiple headers -->
<style>
/* Style adjustments for multiple headers */
.main-header:nth-of-type(1) {
    /* First header (Arabic) - primary display */
    z-index: 1001;
}

.main-header:nth-of-type(2) {
    /* Second header (English) - secondary */
    z-index: 1000;
    display: none; /* Hide by default, can be toggled */
}

.stock-price-banner:nth-of-type(1) {
    /* First stock banner (Arabic) - primary display */
    z-index: 1002;
}

.stock-price-banner:nth-of-type(2) {
    /* Second stock banner (English) - secondary */
    z-index: 999;
    display: none; /* Hide by default */
}

/* Toggle button for switching headers */
.header-toggle {
    position: fixed;
    top: 50px;
    left: 10px; /* Left side for Arabic RTL */
    z-index: 2000;
    background: #003C7F;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    direction: rtl;
}

.header-toggle:hover {
    background: #004B87;
}
</style>



<script>
function toggleHeaders() {
    const arabicHeaders = document.querySelectorAll('.main-header:nth-of-type(1), .stock-price-banner:nth-of-type(1)');
    const englishHeaders = document.querySelectorAll('.main-header:nth-of-type(2), .stock-price-banner:nth-of-type(2)');
    
    arabicHeaders.forEach(header => {
        if (header.style.display === 'none') {
            header.style.display = 'flex';
        } else {
            header.style.display = 'none';
        }
    });
    
    englishHeaders.forEach(header => {
        if (header.style.display === 'none') {
            header.style.display = 'flex';
        } else {
            header.style.display = 'none';
        }
    });
}
</script>

<!-- Add Highcharts libraries -->
<script src="/static/external/code.highcharts.com/stock/highstock.js"></script>
<script src="/static/external/code.highcharts.com/stock/modules/data.js"></script>
<script src="/static/external/code.highcharts.com/stock/modules/exporting.js"></script>
<script src="/static/external/code.highcharts.com/stock/modules/export-data.js"></script>
<script src="/static/external/s3.amazonaws.com/resources.inktankir.com/themes/common/js/linq.min.js"></script>
<script src="/static/external/code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- Custom CSS overrides for Highcharts RTL spacing -->
<style>
/* Add padding to the right of the button container (left visual space in RTL) */
#stock-chart .highcharts-range-selector-buttons {
    padding-right: 30px !important;
}

/* Target the "Zoom" label */
#stock-chart .highcharts-range-selector-buttons .highcharts-label {
    transform: translateX(30px) !important;
}

/* Add space after date inputs to help center separator */
.highcharts-range-input input {
    margin-left: 10px !important; 
}

/* Hero section styling */
.hero-section {
    position: relative;
    height: 300px;
    overflow: hidden;
    width: 100vw;
    margin-top: 80px;
}
.hero-section img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.5));
}
.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    height: 100%;
    display: flex;
    align-items: center;
}
.hero-title {
    color: white;
    font-size: 48px;
    font-weight: 700;
    margin: 0;
}
.hero-subtitle {
    color: white;
    font-size: 24px;
    font-weight: 400;
    margin: 10px 0 0 0;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
    z-index: 2;
    text-align: right;
    direction: rtl;
    font-family: 'Cairo', 'Roboto', Arial, sans-serif;
}
.hero-description {
    color: white;
    font-size: 16px;
    font-weight: 300;
    margin: 10px 0 0 0;
    max-width: 600px;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
    z-index: 2;
    text-align: right;
    direction: rtl;
    font-family: 'Cairo', 'Roboto', Arial, sans-serif;
}

/* Mobile responsive styles for stock information */
@media screen and (max-width: 768px) {
    .hero-section {
        height: 200px !important;
        margin-top: 60px !important;
    }
    .hero-title {
        font-size: 32px !important;
    }
    .hero-subtitle {
        font-size: 18px !important;
        margin-top: 5px !important;
    }
    .hero-description {
        font-size: 14px !important;
        margin-top: 5px !important;
        max-width: 100% !important;
    }
    
    /* Stock information mobile adjustments */
    .stock-info-container {
        flex-direction: column !important;
    }
    .stock-left-panel {
        margin-right: 0 !important;
        padding-right: 20px !important;
        width: 100% !important;
    }
    .stock-right-panel {
        width: 100% !important;
        justify-content: space-between !important;
        padding: 20px !important;
    }
    .stock-price-container {
        font-size: 52px !important;
    }
    .stock-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 15px !important;
        padding: 0 15px !important;
    }
    
    /* RTL mobile adjustments */
    .investment-period {
        flex-direction: column !important;
        align-items: flex-end !important;
    }
    
    /* Improve value organization */
    .stock-grid > div {
        border-bottom: 1px solid #eee !important;
        padding-bottom: 15px !important;
        text-align: right !important;
    }
    
    /* Better organize the stock data panel */
    #msshare-prices h2 {
        font-size: 36px !important;
        padding: 0 15px !important;
    }
    
    /* Improve stock chart container */
    #stock-chart {
        padding: 0 10px !important;
    }
    
    /* Share Information section improvements */
    #msshare-information {
        padding: 0 15px !important;
    }
    
    #msshare-information h2 {
        font-size: 36px !important;
    }
    
    /* Improve share info panel layout */
    .share-info-panel {
        padding: 30px 0 !important;
    }
    
    .share-info-container {
        padding: 0 15px !important;
    }
    
    .share-info-grid {
        display: grid !important;
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 20px !important;
    }
    
    .info-item {
        border-bottom: 1px solid #eee !important;
        padding-bottom: 15px !important;
    }
    
    /* Investment calculator section */
    #msinvestment-calculator {
        padding: 0 15px !important;
    }
    #msinvestment-calculator h2 {
        font-size: 36px !important;
    }
    
    /* Adjust investment calculator form */
    .investment-calculator {
        max-width: 100% !important;
        padding: 0 10px !important;
    }
}

@media screen and (max-width: 480px) {
    .hero-section {
        height: 150px !important;
    }
    .hero-title {
        font-size: 28px !important;
    }
    .hero-subtitle {
        font-size: 16px !important;
        margin-top: 3px !important;
    }
    .hero-description {
        font-size: 12px !important;
        margin-top: 3px !important;
        display: -webkit-box !important;
        -webkit-line-clamp: 2 !important;
        -webkit-box-orient: vertical !important;
        overflow: hidden !important;
    }
    main {
        margin-top: 0 !important;
    }
    
    /* Further adjustments for smallest screens */
    .stock-right-panel {
        flex-direction: column !important;
        align-items: flex-end !important; /* RTL alignment */
        gap: 15px !important;
    }
    .stock-price-container {
        font-size: 42px !important;
    }
    .change-percentage {
        font-size: 28px !important;
    }
    .stock-grid {
        grid-template-columns: 1fr !important;
    }
    
    /* Improve value organization on smallest screens */
    .stock-grid > div {
        display: flex !important;
        flex-direction: row-reverse !important; /* RTL layout */
        justify-content: space-between !important;
        align-items: center !important;
    }
    
    .stock-grid > div > div:first-child {
        font-size: 10px !important;
    }
    
    /* Adjust headers for tighter spacing */
    #msshare-prices h2, #msinvestment-calculator h2, #msshare-information h2 {
        font-size: 30px !important;
        margin-bottom: 25px !important;
    }
    
    /* Improve chart layout */
    .highcharts-container {
        width: 100% !important;
    }
    
    /* Fix investment calculator form alignment */
    .investment {
        flex-wrap: wrap !important;
        justify-content: flex-end !important;
    }
    
    .investment p {
        width: 100% !important;
        text-align: right !important;
        margin-bottom: 5px !important;
    }
    
    /* Adjust table for small screens */
    .table {
        font-size: 14px !important;
    }
    
    .table th, .table td {
        padding: 8px 5px !important;
    }
    
    /* Share information small screen optimizations */
    .share-info-grid {
        grid-template-columns: 1fr !important;
    }
    
    .info-item {
        display: flex !important;
        flex-direction: row-reverse !important; /* RTL direction */
        justify-content: space-between !important;
        align-items: center !important;
        padding-bottom: 10px !important;
        margin-bottom: 10px !important;
    }
    
    .info-label, .info-value {
        margin: 0 !important;
        font-size: 14px !important;
    }
    
    .info-label {
        font-weight: 700 !important;
    }
}
</style>

<script type="text/javascript">
    function updateStockValues(data) {
        // Update main values
        $('#last-trade-time').text(data.last_trade_time);
        $('#last-trade-price').text(data.last_trade_price);
        $('#net-last-change').text(data.net_last_change);
        $('#last-change-percentage').text(data.last_change_percentage);
        
        // Update price info
        $('#today-high-price').text(data.today_high_price);
        $('#today-low-price').text(data.today_low_price);
        $('#today-open-price').text(data.today_open_price);
        $('#today-close-price').text(data.today_close_price);
        
        // Update volume and turnover
        $('#today-total-volume').text(data.today_total_volume);
        $('#today-total-value').text(data.today_total_value);
        $('#today-total-trades').text(data.today_total_trades);
        
        // Update 52 week high/low
        $('#year-high-price').text(data.year_high_price);
        $('#year-low-price').text(data.year_low_price);

        // Update change color based on value
        const changeValue = parseFloat(data.net_last_change);
        const changeElement = $('#net-last-change').parent();
        const percentElement = $('#last-change-percentage');
        
        if (changeValue > 0) {
            changeElement.css('background', '#22c55e');
            percentElement.css('color', '#22c55e');
        } else if (changeValue < 0) {
            changeElement.css('background', '#ef4444');
            percentElement.css('color', '#ef4444');
        } else {
            changeElement.css('background', '#666');
            percentElement.css('color', '#666');
        }
    }

    function refreshStockData() {
        $.ajax({
            url: window.location.pathname,
            data: { refresh: 'true' },
            success: function(response) {
                if (response && response.stock_data) {
                    updateStockValues(response.stock_data);
                    
                    // Update chart if it exists
                    if (window.stockChart && response.historical_data) {
                        const chartData = JSON.parse(response.historical_data);
                        window.stockChart.series[0].setData(chartData);
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('Error updating stock data:', error);
            }
        });
    }

    // Refresh every 30 seconds (30000 milliseconds)
    $(document).ready(function() {
        // Add IDs to elements for easier updating
        $('.stock-price').attr('id', 'last-trade-price');
        $('.change-value').attr('id', 'net-last-change');
        $('.change-percentage').attr('id', 'last-change-percentage');
        $('.last-update-time').attr('id', 'last-trade-time');
        $('.today-high').attr('id', 'today-high-price');
        $('.today-low').attr('id', 'today-low-price');
        $('.today-open').attr('id', 'today-open-price');
        $('.today-close').attr('id', 'today-close-price');
        $('.today-volume').attr('id', 'today-total-volume');
        $('.today-turnover').attr('id', 'today-total-value');
        $('.today-trades').attr('id', 'today-total-trades');
        $('.year-high').attr('id', 'year-high-price');
        $('.year-low').attr('id', 'year-low-price');

        // Initial refresh
        refreshStockData();
        
        // Set up periodic refresh
        setInterval(refreshStockData, 30000);
    });
</script>
{% endblock %}

{% block content %}
<!-- Add required styles -->
<style>
input[type="date"] {
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    background-color: white;
    cursor: pointer;
    text-align: right;
    direction: rtl;
}

input[type="date"]:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 5px rgba(0,123,255,0.3);
}

.investment-period {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    justify-content: flex-end;
}

.investment-period p {
    margin: 0;
    padding: 0 5px;
}

input[type="button"] {
    padding: 8px 16px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

input[type="button"]:hover {
    background-color: #0056b3;
}

.investment-calculator {
    direction: rtl;
    max-width: 600px;
    margin: 0 auto;
}

.investment {
    text-align: right;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.investment p {
    margin: 0;
    white-space: nowrap;
}

.investment-value {
    display: inline-flex;
    margin: 0 10px;
}

.investment-type {
    display: inline-flex;
}

.table {
    direction: rtl;
    text-align: right;
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

/* Ensure all tables have RTL direction */
table {
    direction: rtl;
}

.table th, .table td {
    padding: 10px;
    border: 1px solid #ddd;
    text-align: right;
}

.table th {
    background-color: #f6f6f6;
}
</style>

<main style="margin-top: 0; direction: rtl;">
    <!-- Hero Section - Arabic Content (RTL) -->
    <div class="hero-section">
        <img src="/static/external/s3.amazonaws.com/resources.inktankir.com/themes/gbautov2/img/slides/bayon2.jpg" alt="معلومات الأسهم">
        <div class="hero-overlay">
            <div class="hero-content">
                <h1 class="hero-title">معلومات الأسهم</h1>
            </div>
        </div>
    </div>

    <div class="main-section">
        <div class="container" style="padding: 0;">
            {% if data_source == 'none' %}
                <div class="alert alert-warning">
                    {{ error_message }}
                </div>
            {% else %}
                <!-- Share Prices Section -->
                <div id="msshare-prices" style="margin-bottom: 80px;">
                    
                    <!-- Stock Information Panel -->
                    <div style="background: white; padding: 30px 0; margin: 20px 0;">
                        <div style="max-width: 1200px; margin: 0 auto; padding: 0;">
                            <div class="stock-info-container" style="display: flex; justify-content: space-between; align-items: center;">
                                <!-- Right panel (RTL layout) with gray background that stretches to the right -->
                                <div class="stock-left-panel" style="background: #f8f8f8; padding: 30px 40px; position: relative; margin-right: -100vw; padding-right: 100vw; text-align: right;">
                                    <div class="last-update-time" style="font-size: 14px; color: #666; margin-bottom: 8px; font-family: 'Cairo', 'Helvetica Neue', Arial, sans-serif; font-weight: 400;">{{ last_trade_time }}</div>
                                    <div style="font-size: 64px; font-weight: 200; font-family: 'Cairo', 'Helvetica Neue', Arial, sans-serif; letter-spacing: 1px;">GBCO.CA</div>
                                </div>
                                
                                <!-- Left panel (RTL layout) -->
                                <div class="stock-right-panel" style="display: flex; align-items: center; gap: 40px; padding-left: 20px; justify-content: flex-end;">
                                    <div>
                                        <div style="font-size: 14px; color: #666; margin-bottom: 4px; font-family: 'Cairo', 'Helvetica Neue', Arial, sans-serif; font-weight: 400; text-align: right;">النسبة المئوية للتغير</div>
                                        <div class="change-percentage stock-change-value {% if net_last_change > 0 %}positive{% elif net_last_change < 0 %}negative{% else %}neutral{% endif %}" style="font-size: 36px; font-weight: 400; font-family: 'Cairo', 'Helvetica Neue', Arial, sans-serif; text-align: right;">{{ last_change_percentage }}</div>
                                    </div>
                                    <div>
                                        <div style="font-size: 14px; color: #666; margin-bottom: 4px; font-family: 'Cairo', 'Helvetica Neue', Arial, sans-serif; font-weight: 400; text-align: right;">التغير</div>
                                        <div class="change-value-container {% if net_last_change > 0 %}positive{% elif net_last_change < 0 %}negative{% else %}neutral{% endif %}" style="color: white; padding: 4px 12px; border-radius: 6px; font-size: 20px; font-weight: 400; font-family: 'Cairo', 'Helvetica Neue', Arial, sans-serif; text-align: right;"><span class="change-value">{{ net_last_change }}</span></div>
                                    </div>
                                    <div style="text-align: right;">
                                        <div class="stock-price-container" style="font-size: 72px; font-weight: 600; font-family: 'Cairo', 'Helvetica Neue', Arial, sans-serif;"><span class="stock-price">{{ last_trade_price }}</span><span style="font-size: 24px; margin-right: 5px; font-weight: 300;">جنيه</span></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Bottom Row -->
                            <div class="stock-grid" style="display: grid; grid-template-columns: repeat(5, 1fr); gap: 20px; margin-top: 40px; direction: rtl;">
                                <!-- Day's High -->
                                <div>
                                    <div style="font-size: 12px; color: #000; font-weight: 800; margin-bottom: 8px; font-family: 'Cairo', sans-serif; text-transform: uppercase; letter-spacing: 0.5px;">أعلى سعر اليوم</div>
                                    <div style="font-size: 24px; color: #666; font-weight: 200; font-family: 'Cairo', sans-serif;">
                                        <span class="today-high">{{ today_high_price }}</span>
                                        <span style="font-size: 16px; color: #999; font-weight: 300;">جم</span>
                                    </div>
                                </div>

                                <!-- Open -->
                                <div>
                                    <div style="font-size: 12px; color: #000; font-weight: 800; margin-bottom: 8px; font-family: 'Cairo', sans-serif; text-transform: uppercase; letter-spacing: 0.5px;">سعر الافتتاح</div>
                                    <div style="font-size: 24px; color: #666; font-weight: 200; font-family: 'Cairo', sans-serif;">
                                        <span class="today-open">{{ today_open_price }}</span>
                                        <span style="font-size: 16px; color: #999; font-weight: 300;">جم</span>
                                    </div>
                                </div>

                                <!-- Volume -->
                                <div>
                                    <div style="font-size: 12px; color: #000; font-weight: 800; margin-bottom: 8px; font-family: 'Cairo', sans-serif; text-transform: uppercase; letter-spacing: 0.5px;">حجم التداول</div>
                                    <div style="font-size: 24px; color: #666; font-weight: 200; font-family: 'Cairo', sans-serif;">
                                        <span class="today-volume">{{ today_total_volume }}</span>
                                    </div>
                                </div>

                                <!-- 52-Week High -->
                                <div>
                                    <div style="font-size: 12px; color: #000; font-weight: 800; margin-bottom: 8px; font-family: 'Cairo', sans-serif; text-transform: uppercase; letter-spacing: 0.5px;">أعلى سعر خلال ٥٢ أسبوع</div>
                                    <div style="font-size: 24px; color: #666; font-weight: 200; font-family: 'Cairo', sans-serif;">
                                        <span class="year-high" id="year-high-price">{{ year_high_price }}</span>
                                        <span style="font-size: 16px; color: #999; font-weight: 300;">جم</span>
                                    </div>
                                </div>

                                <!-- Earnings Per Share -->
                                <div>
                                    <div style="font-size: 12px; color: #000; font-weight: 800; margin-bottom: 8px; font-family: 'Cairo', sans-serif; text-transform: uppercase; letter-spacing: 0.5px;">ربحية السهم</div>
                                    <div style="font-size: 24px; color: #666; font-weight: 200; font-family: 'Cairo', sans-serif;">
                                        1.68
                                        <span style="font-size: 16px; color: #999; font-weight: 300;">جم</span>
                                    </div>
                                    <div style="font-size: 12px; color: #999; font-family: 'Cairo', sans-serif; font-weight: 400;">بناءً على الربع الرابع 2023</div>
                                </div>

                                <!-- Day's Low -->
                                <div>
                                    <div style="font-size: 12px; color: #000; font-weight: 800; margin-bottom: 8px; font-family: 'Cairo', sans-serif; text-transform: uppercase; letter-spacing: 0.5px;">أدنى سعر اليوم</div>
                                    <div style="font-size: 24px; color: #666; font-weight: 200; font-family: 'Cairo', sans-serif;">
                                        <span class="today-low">{{ today_low_price }}</span>
                                        <span style="font-size: 16px; color: #999; font-weight: 300;">جم</span>
                                    </div>
                                </div>

                                <!-- Prev. Close -->
                                <div>
                                    <div style="font-size: 12px; color: #000; font-weight: 800; margin-bottom: 8px; font-family: 'Cairo', sans-serif; text-transform: uppercase; letter-spacing: 0.5px;">سعر الإغلاق السابق</div>
                                    <div style="font-size: 24px; color: #666; font-weight: 200; font-family: 'Cairo', sans-serif;">
                                        <span class="today-close">{{ today_close_price }}</span>
                                        <span style="font-size: 16px; color: #999; font-weight: 300;">جم</span>
                                    </div>
                                </div>

                                <!-- Turnover -->
                                <div>
                                    <div style="font-size: 12px; color: #000; font-weight: 800; margin-bottom: 8px; font-family: 'Cairo', sans-serif; text-transform: uppercase; letter-spacing: 0.5px;">قيمة التداول</div>
                                    <div style="font-size: 24px; color: #666; font-weight: 200; font-family: 'Cairo', sans-serif;">
                                        <span class="today-turnover">{{ today_total_value }}</span>
                                        <span style="font-size: 16px; color: #999; font-weight: 300;">جم</span>
                                    </div>
                                </div>

                                <!-- 52-Week Low -->
                                <div>
                                    <div style="font-size: 12px; color: #000; font-weight: 800; margin-bottom: 8px; font-family: 'Cairo', sans-serif; text-transform: uppercase; letter-spacing: 0.5px;">أدنى سعر خلال ٥٢ أسبوع</div>
                                    <div style="font-size: 24px; color: #666; font-weight: 200; font-family: 'Cairo', sans-serif;">
                                        <span class="year-low" id="year-low-price">{{ year_low_price }}</span>
                                        <span style="font-size: 16px; color: #999; font-weight: 300;">جم</span>
                                    </div>
                                </div>

                                <!-- P/E Ratio -->
                                <div>
                                    <div style="font-size: 12px; color: #000; font-weight: 800; margin-bottom: 8px; font-family: 'Cairo', sans-serif; text-transform: uppercase; letter-spacing: 0.5px;">مضاعف الربحية</div>
                                    <div style="font-size: 24px; color: #666; font-weight: 200; font-family: 'Cairo', sans-serif;">
                                        7.20
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <section class="section section--sm">
                        <div id="stock-chart"></div>
                    </section>
                </div>

                <!-- Share Information Section with improved classes for mobile -->
                <div id="msshare-information" class="share-info-section" style="margin-bottom: 80px;">
                    <h2 class="section-title" style="font-size: 48px; color: #003C7F; margin-bottom: 40px; font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; font-weight: 700; letter-spacing: -0.5px; max-width: 1200px; margin: 0 auto 40px; padding: 0; text-align: right;">معلومات الأسهم</h2>
                    
                    <div class="share-info-container">
                        <!-- Include share info content with proper classes -->
                        {% include "website/ir.gb-corporation.com/ar/share-info.html" %}
                    </div>
                </div>

                <!-- Investment Calculator Section -->
                <div id="msinvestment-calculator" style="margin-bottom: 80px;">
                    {% include "website/ir.gb-corporation.com/ar/investment-calculator.html" %}
                </div>
            {% endif %}
        </div>
    </div>
</main>

<!-- Continue with the rest of the content -->
<div style="margin-bottom: 60px;"></div>



<script type="text/javascript">
document.addEventListener('DOMContentLoaded', function() {
    var stockChart = $('#stock-chart');
    if (stockChart.length && '{{ data_source }}' !== 'none') {
        // Set language options for range selector labels (Arabic)
        Highcharts.setOptions({
            lang: {
                rangeSelectorZoom: 'تكبير',
                shortMonths: [
                    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
                ]
            }
        });

        fetch('/api/stock-history/?chart=true')
            .then(response => response.json())
            .then(data => {
                // Transform data for Highcharts
                const ohlc = [];
                const volume = [];
                
                data.forEach(item => {
                    const timestamp = new Date(item.date).getTime();
                    ohlc.push([
                        timestamp,
                        parseFloat(item.close_price)
                    ]);
                    volume.push([
                        timestamp,
                        parseFloat(item.volume)
                    ]);
                });

                // Sort data by date
                ohlc.sort((a, b) => a[0] - b[0]);

                // Create the chart with existing styling
                Highcharts.stockChart('stock-chart', {
                    chart: {
                        zoomType: 'x',
                        panning: {
                            enabled: true,
                            type: 'x'
                        },
                        panKey: 'shift'
                    },

                    rangeSelector: {
                        selected: 1,
                        inputEnabled: true,
                        inputDateFormat: '%b %e, %Y',
                        inputBoxBorderColor: 'transparent',
                        inputBoxStyle: {
                           backgroundColor: 'transparent'
                        },
                        inputStyle: {
                           color: '#666666',
                           fontWeight: 'normal',
                           fontSize: '14px'
                        },
                        labelStyle: {
                           color: '#666666',
                           fontWeight: 'normal',
                           fontSize: '14px'
                        },
                        buttonTheme: {
                            fill: 'none',
                            stroke: 'none',
                            style: {
                                fontSize: '14px'
                            },
                            states: {
                                hover: {
                                    fill: '#f0f0f0',
                                    stroke: 'none'
                                },
                                select: {
                                    fill: 'none',
                                    stroke: 'none',
                                    style: {
                                        fontWeight: 'bold'
                                    }
                                }
                            }
                        },
                        buttons: [{
                            type: 'month',
                            count: 1,
                            text: '١ش'
                        }, {
                            type: 'month',
                            count: 3,
                            text: '٣ش'
                        }, {
                            type: 'month',
                            count: 6,
                            text: '٦ش'
                        }, {
                            type: 'ytd',
                            text: 'YTD'
                        }, {
                            type: 'year',
                            count: 1,
                            text: '١س'
                        }, {
                            type: 'all',
                            text: 'الكل'
                        }]
                    },

                   
                    credits: {
                        enabled: false
                    },
                    
                    exporting: {
                        enabled: false
                    },

                    // Add main tooltip configuration for RTL
                    tooltip: {
                        useHTML: true,
                        style: {
                            direction: 'rtl',
                            textAlign: 'right'
                        },
                        valueDecimals: 2
                    },

                    colors: ["#3366CC", "#3366CC", "#3366CC", "#3366CC", "B3B3B3"],

                    plotOptions: {
                        areaspline: {
                            smoothing: 1.5
                        },
                        series: {
                            allowPointSelect: true,
                            cursor: 'crosshair'
                        }
                    },

                    navigator: {
                        height: 50,
                        margin: 10,
                        series: {
                            color: '#3366CC',
                            fillOpacity: 0.05,
                            lineWidth: 2
                        }
                    },

                    scrollbar: {
                        enabled: true,
                        height: 20
                    },

                    xAxis: {
                        type: 'datetime',
                        ordinal: true,
                        opposite: true,
                        gridLineWidth: 0,
                        lineColor: '#C0C0C0',
                        tickColor: '#C0C0C0',
                        labels: {
                            style: {
                                color: '#666666',
                                fontFamily: 'Cairo, sans-serif'
                            }
                        },
                        events: {
                            afterSetExtremes: function(e) {
                                // Enable zooming behavior
                                if (e.trigger !== 'syncExtremes') {
                                    this.chart.showResetZoom();
                                }
                            }
                        }
                    },

                    yAxis: {
                        opposite: true,
                        gridLineColor: '#EEEEEE',
                        labels: {
                            align: 'left',
                            x: 10,
                            style: {
                                color: '#666666',
                                fontFamily: 'Cairo, sans-serif'
                            }
                        },
                        title: {
                            text: null
                        },
                        plotLines: [{
                            value: 0,
                            width: 1,
                            color: '#C0C0C0'
                        }]
                    },

                    series: [{
                        name: 'سعر السهم',
                        data: ohlc,
                        type: 'line',
                        threshold: null,
                        lineWidth: 2,
                        states: {
                            hover: {
                                lineWidth: 2
                            }
                        },
                        color: '#3366CC',
                        marker: {
                            enabled: false
                        }
                    }]
                });
            })
            .catch(error => {
                console.error('Error loading stock history:', error);
                stockChart.html('<div class="alert alert-warning">Unable to load stock chart data at this time.</div>');
            });
    }
});

// Set up date constraints
window.addEventListener('load', function() {
    const startDate = document.getElementById('startperiod');
    const endDate = document.getElementById('endperiod');
    
    // Set max date to today for both inputs
    const today = new Date().toISOString().split('T')[0];
    startDate.max = today;
    endDate.max = today;
    
    // Set min date to 2007 for start date (company's listing date)
    startDate.min = '2007-07-09';
    
    // Update min/max dates when either date changes
    startDate.addEventListener('change', function() {
        endDate.min = this.value;
        if (endDate.value && endDate.value < this.value) {
            endDate.value = this.value;
        }
    });
    
    endDate.addEventListener('change', function() {
        startDate.max = this.value;
        if (startDate.value && startDate.value > this.value) {
            startDate.value = this.value;
        }
    });
});

// Investment calculator function is now included in the investment-calculator.html template
</script>

<!-- Styles for RTL layout -->
<style>
.footer, footer {
    background: #ffffff !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    box-shadow: none !important;
}

.change-value-container.positive {
    background-color: #22c55e;
}
.change-value-container.negative {
    background-color: #ef4444;
}
.change-value-container.neutral {
    background-color: #666;
}
.stock-change-value.positive {
    color: #22c55e;
}
.stock-change-value.negative {
    color: #ef4444;
}
.stock-change-value.neutral {
    color: #666;
}
</style>


{% endblock %}

