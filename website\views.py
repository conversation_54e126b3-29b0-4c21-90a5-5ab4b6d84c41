from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import TemplateView, ListView, DetailView
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from django.db.models import Q, Count, Max, Min # Added Count, Max, Min for 52-week calculations

from django.urls import reverse
from django.views.decorators.http import require_http_methods

import os
import json
import logging
import pytz
from datetime import datetime, timedelta
from decimal import Decimal
import subprocess
import re

# Import security utilities
from api.tools import get_client_ip

from .models import (
    MenuItem, Page, Slider, BoardMember, FinancialReport,
    PressRelease, IRCalendarEvent, AnalystCoverage,
    BusinessSegment, CorporateGovernance, ESGInitiative,
    ContactPerson, Dividend, CompanyHistory, FinancialMetric,
    ShareholdingStructure, Shareholder, StockExchangeListing,
    InvestorPresentation, RegulatoryFiling, Award,
    FAQ, Subscriber, DocumentCategory, Document,
    MediaGallery, MediaItem, MarketData, SocialMediaFeed,
    ESGPolicy, ESGReport, BusinessSegmentDetail, MarketResearch,
    InvestorToolCalculator, StockPrice, InvestorResource,
    WebsiteSetting, SubscriptionPreference, StockAlert,
    CorporateDisclosure, ShareholdingStructureDisclosure, # Added CorporateDisclosure explicitly
    CorporateInformation, AnnualReport, InvestorFactsheet,
    FinancialResult, Fundamentals, FinancialStatement,
    CorporateAction, BoardCommittee, CommitteeMember,
    AtAGlanceOverview, BusinessSegmentOverview, BusinessSegmentKeyPoint,
    BusinessSegmentBrand, CompanyProfile, CompanyVisionMission,
    CompanyCoreValue, HistoryContent, HistoryTimeline,
    Strategy, IRContact, IROfficeLocation,
    StockHistory, AnalystToolkit, CompanyOverview,
    FinancialSnapshot,
    # Add new models here
    FinancialHighlight, EarningsResult, News, NewsroomArticle,
    PublicationCategory, Publication, Year, Factsheet, Shareholder,
    FundamentalsShortcut,
    HeroSection, HomeGallery,
    NewsletterSubscription  # Add this import
)
from .utils import get_common_context
from core.stock_data import StockDataService

# Configure logger
logger = logging.getLogger(__name__)

def get_52_week_high_low():
    """Calculate 52-week high and low values from StockHistory"""
    try:
        # Get date 52 weeks ago
        fifty_two_weeks_ago = timezone.now().date() - timedelta(weeks=52)
        
        # Query StockHistory for the past 52 weeks
        yearly_data = StockHistory.objects.filter(
            date__gte=fifty_two_weeks_ago,
            is_trading_day=True
        ).aggregate(
            year_high=Max('high_price'),
            year_low=Min('low_price')
        )
        
        return {
            'year_high_price': yearly_data['year_high'] or 0,
            'year_low_price': yearly_data['year_low'] or 0
        }
    except Exception as e:
        logger.error(f"Error calculating 52-week high/low: {str(e)}")
        return {
            'year_high_price': 0,
            'year_low_price': 0
        }

def get_menu_items(language='en'):
    # Get all root level menu items (no parent)
    root_items = MenuItem.objects.filter(
        language=language,
        is_active=True,
        parent__isnull=True
    ).order_by('order')

    menu_structure = []
    for root_item in root_items:
        # Get all children for this root item
        children = MenuItem.objects.filter(
            parent=root_item,
            is_active=True,
            language=language
        ).order_by('order')
        
        # Ensure URL starts with language code for proper routing
        root_url = root_item.url
        if root_url:
            if not root_url.startswith('/'):
                root_url = f'/{root_url}'
            if not root_url.startswith(f'/{language}/'):
                # Remove .html extension if present
                root_url = root_url.replace('.html', '')
                root_url = f'/{language}{root_url}.html'
        
        menu_item = {
            'title': root_item.title,
            'url': root_url,
            'order': root_item.order,
            'children': []
        }
        
        # Process children
        for child in children:
            child_url = child.url
            if child_url:
                if not child_url.startswith('/'):
                    child_url = f'/{child_url}'
                if not child_url.startswith(f'/{language}/'):
                    # Remove .html extension if present
                    child_url = child_url.replace('.html', '')
                    child_url = f'/{language}{child_url}.html'
                
            menu_item['children'].append({
                'title': child.title,
                'url': child_url,
                'order': child.order
            })
        
        menu_structure.append(menu_item)
    
    # Sort by order
    menu_structure.sort(key=lambda x: x['order'])
    for item in menu_structure:
        item['children'].sort(key=lambda x: x['order'])
    
    return menu_structure

def get_common_context(language='en'):
    """Get common context data for all pages"""
    context = {
        'language': language,
        'current_year': datetime.now().year,
        'menu_items': get_menu_items(language),
        'settings': get_settings(language),
        'hero_sections': get_hero_sections(language=language),
        'home_gallery': get_home_gallery(language=language) if language == get_language() else []
    }
    return context

def index(request):
    """Homepage view"""
    # Get the user's language
    language = get_language()
    
    # Get common context first
    context = get_common_context(language)
    
    # Get hero sections specifically for the home page
    context['home_hero'] = get_hero_sections(page='home', language=language)
    
    # Get home gallery items
    context['gallery_items'] = get_home_gallery(language=language)
    
    try:
        # Force-refresh presentations data to avoid caching issues
        # Get the investor presentation data directly (without any filtering)
        all_presentations = list(InvestorPresentation.objects.all())
        
        investor_presentations_qs = InvestorPresentation.objects.filter(
            language=language, is_active=True
        ).order_by('-presentation_date')
        
        # Force evaluate queryset and put in context
        investor_presentations_list = list(investor_presentations_qs)
        
        # FALLBACK: If the list is empty but there are presentations in the database,
        # let's try to use the unfiltered presentations as a temporary solution
        if not investor_presentations_list and all_presentations:
            investor_presentations_list = all_presentations[:1]  # Just use the first one as a fallback
            
        context['investor_presentations'] = investor_presentations_list
        
        # Annual reports - same approach
        all_reports = list(AnnualReport.objects.all())
        
        annual_reports_qs = AnnualReport.objects.filter(
            language=language, is_active=True
        ).order_by('-year')
        
        # Force evaluate queryset and put in context
        annual_reports_list = list(annual_reports_qs)
        
        # FALLBACK: Same approach as presentations
        if not annual_reports_list and all_reports:
            annual_reports_list = all_reports[:1]
            
        context['annual_reports'] = annual_reports_list
        
        # ESG reports - same approach
        all_esg = list(ESGReport.objects.all())
        
        esg_reports_qs = ESGReport.objects.filter(
            language=language, is_active=True
        ).order_by('-year', '-publish_date')
        
        # Force evaluate queryset and put in context
        esg_reports_list = list(esg_reports_qs)
        
        # FALLBACK: Same approach as presentations
        if not esg_reports_list and all_esg:
            esg_reports_list = all_esg[:1]
            
        context['esg_reports'] = esg_reports_list
    except Exception as e:
        # Set empty lists as fallbacks in case of errors
        context['investor_presentations'] = []
        context['annual_reports'] = []
        context['esg_reports'] = []
    
    # Add URL to check cache
    context['context_check_url'] = '/check-context/'

    # Get active years directly from the database
    active_years = Year.objects.filter(is_active=True).order_by('-year')
    context['years'] = [y.year for y in active_years] # Pass list of year numbers
    
    # Get all earnings results
    all_results = EarningsResult.objects.all().order_by('-year', 'period')
    result_years = sorted(list(set(all_results.values_list('year', flat=True))), reverse=True)
    context['years'] = result_years

    # Group results by year using the determined years list
    context['earnings_results_by_year'] = {}
    for year_val in context['years']:
        context['earnings_results_by_year'][year_val] = all_results.filter(year=year_val)
    
    # Get shareholders data
    latest_structure = ShareholdingStructure.objects.filter(
        language=language
    ).order_by('-year', '-period').first()
    
    if latest_structure:
        shareholders = latest_structure.shareholders.all()
        shareholders_json = json.dumps([{"name": s.name, "percentage": float(s.percentage)} for s in shareholders])
        context['shareholders'] = shareholders
        context['shareholders_json'] = shareholders_json
    else:
        # Default data if no shareholders exist
        default_shareholders_json = json.dumps([
            {"name": "Major Shareholders", "percentage": 70.0},
            {"name": "Free Float", "percentage": 30.0}
        ])
        context['shareholders'] = []
        context['shareholders_json'] = default_shareholders_json
    
    # Add financial highlights data for the homepage
    financial_highlights_data = get_financial_highlights_data(language)
    context['financial_highlights'] = financial_highlights_data['highlights']
    context['latest_earnings'] = financial_highlights_data['latest_earnings']
    
    # Add latest news for the homepage
    context['latest_news'] = News.objects.all().order_by('-publish_date')[:3]
    
    # Add newsroom article data for the homepage  
    context['newsroom_article'] = NewsroomArticle.objects.all().order_by('-date').first()

    # Add any other required context variables directly
    context['sliders'] = Slider.objects.filter(language=language, is_active=True)
    context['upcoming_events'] = IRCalendarEvent.objects.filter(
        start_date__gte=timezone.now(),
        language=language,
        is_active=True
    ).order_by('start_date')[:3]

    template_path = f'website/ir.gb-corporation.com/index_{language}.html'
    return render(request, template_path, context)

def en_page(request, page_name=None):
    # Template path logic...
    if page_name is None:
        # Handle case where no page_name is provided, maybe redirect or render a default
        # For now, assume it might default to index or similar, adjust as needed
        template_path = os.path.join('website', 'ir.gb-corporation.com', 'en', 'index.html') # Example default
        page_name = 'index' # Set page_name for context fetching
    else:
        # Check if page_name already has .html extension
        if page_name.endswith('.html'):
            template_path = os.path.join('website', 'ir.gb-corporation.com', 'en', page_name)
        else:
            template_path = os.path.join('website', 'ir.gb-corporation.com', 'en', f'{page_name}.html')

    language = 'en'
    context = get_common_context(language)
    # Fetch general page context first
    context.update(get_page_context(request, page_name, language))

    # --- ADD LOGGING BEFORE RENDER ---
    logger.info(f"--- [en_page for '{page_name}'] Final context before rendering: ---")
    # Log specific items we care about for ir-contacts
    if page_name == 'ir-contacts':
        logger.info(f"Context has 'ir_contacts': {'ir_contacts' in context}")
        if 'ir_contacts' in context:
            logger.info(f"---> ir_contacts count: {len(context['ir_contacts'])}") # Use len() for queryset/list
        logger.info(f"Context has 'office': {'office' in context}")
        if 'office' in context and context['office'] is not None:
            office_obj = context['office']
            logger.info(f"---> office object ID: {office_obj.id}")
            logger.info(f"---> office.address: '{office_obj.address}'") 
            logger.info(f"---> office.address_ar: '{office_obj.address_ar}'")
            logger.info(f"---> office.email: '{office_obj.email}'")
            logger.info(f"---> office.phone: '{office_obj.phone}'")
            logger.info(f"---> office.fax: '{office_obj.fax}'")
        elif 'office' in context:
            logger.info(f"---> office is None in context")
        else:
            logger.info(f"---> office key missing from context")
    logger.info(f"--- [en_page for '{page_name}'] End of context log ---")
    # --- END LOGGING BEFORE RENDER ---

    # Add specific context for the "filings" page
    if page_name == 'filings':
        print(f"--- DEBUG (en): Inside filings context block ---") # DEBUG
        try:
            latest_shortcut = FundamentalsShortcut.objects.filter(language=language).order_by('-year', '-period').first()
            print(f"--- DEBUG (en): Fetched latest_shortcut: {latest_shortcut} ---") # DEBUG
            if latest_shortcut:
                 print(f"--- DEBUG (en): Found latest_shortcut: {latest_shortcut} ---") # DEBUG
            context['fundamentals_shortcut'] = latest_shortcut
        except Exception as e:
            print(f"--- DEBUG (en): Error fetching shortcut: {e} ---") # DEBUG
            context['fundamentals_shortcut'] = None

        try:
            latest_annual_report = AnnualReport.objects.filter(language=language, is_active=True).order_by('-year').first()
            context['annual_reports_url'] = latest_annual_report.pdf_file.url if latest_annual_report and latest_annual_report.pdf_file else None
        except AnnualReport.DoesNotExist:
            context['annual_reports_url'] = None
        
        # Add context for the financial data tabs (Snapshot, IS, BS)
        try:
            # Get all fundamental data for the snapshot table (multiple periods)
            context['financial_data'] = Fundamentals.objects.filter(language=language, is_active=True).order_by('-date')[:4] # Get latest 4 for snapshot
            # Get the single latest fundamental data for IS and BS tabs
            context['latest_fundamental'] = Fundamentals.objects.filter(language=language, is_active=True).order_by('-date').first()
        except Fundamentals.DoesNotExist:
            context['financial_data'] = []
            context['latest_fundamental'] = None
            
        # Add context for Dividends table
        context['dividends'] = Dividend.objects.filter(is_active=True).order_by('-payment_date')

        # Add context for Factsheets (grouped by year)
        all_factsheets = Factsheet.objects.all().order_by('-year__year', '-period') # Ensure correct ordering
        factsheet_years = sorted(list(set(all_factsheets.values_list('year__year', flat=True))), reverse=True)
        factsheets_by_year = {year: [] for year in factsheet_years}
        for fs in all_factsheets:
             if fs.year.year in factsheets_by_year:
                 factsheets_by_year[fs.year.year].append(fs)

        context['factsheet_years'] = factsheet_years
        context['factsheets_by_year'] = factsheets_by_year

    # Add specific context for news-room page
    if page_name == 'news-room':
        selected_category = request.GET.get('category', 'press_releases')
        selected_year_str = request.GET.get('year', 'All') # Changed default from 'latest' to 'All'

        # Get available years for filters
        press_release_years = PressRelease.objects.filter(language=language, is_active=True).values_list('publish_date__year', flat=True).distinct()
        disclosure_years = CorporateDisclosure.objects.filter(language=language, is_active=True).values_list('disclosure_date__year', flat=True).distinct()
        available_years = sorted(list(set(list(press_release_years) + list(disclosure_years))), reverse=True)

        # Determine selected year
        latest_year = available_years[0] if available_years else None
        if selected_year_str == 'latest':
            selected_year = latest_year
        elif selected_year_str == 'All':
            selected_year = None # No year filter
        else:
            try:
                selected_year = int(selected_year_str)
                if selected_year not in available_years: # Validate selected year exists
                    selected_year = latest_year
                    selected_year_str = 'latest' # Reset str if invalid year requested
            except (ValueError, TypeError):
                selected_year = latest_year
                selected_year_str = 'latest' # Reset str if invalid

        # Fetch data based on category and year
        items_list = []
        if selected_category == 'press_releases':
            query = PressRelease.objects.filter(language=language, is_active=True)
            if selected_year:
                query = query.filter(publish_date__year=selected_year)
            releases = query.order_by('-publish_date')
            for item in releases:
                file_url = item.file.url if item.file else '#'
                items_list.append({
                    'type': 'press_release',
                    'title': item.title,
                    'date': item.publish_date,
                    'url': file_url,
                    'format': 'PDF'
                })
        elif selected_category == 'disclosures':
            query = CorporateDisclosure.objects.filter(language=language, is_active=True)
            if selected_year:
                query = query.filter(disclosure_date__year=selected_year)
            disclosures = query.order_by('-disclosure_date')
            for item in disclosures:
                file_url = item.document.url if item.document else '#'
                items_list.append({
                    'type': 'disclosure',
                    'title': item.title,
                    'date': item.disclosure_date,
                    'url': file_url,
                    'format': 'PDF' if item.document else 'N/A'
                })

        # Keep Newsroom Articles separate for top section
        newsroom_articles_qs = NewsroomArticle.objects.all().order_by('-date')
        newsroom_articles_list = list(newsroom_articles_qs)
        print(f"--- DEBUG en_page(news-room): Newsroom Articles Count: {len(newsroom_articles_list)}") # Keep debug

        # Update context for news-room specifically
        context.update({
            'page_title': 'News Room',
            'newsroom_articles': newsroom_articles_list, # For top section
            'press_release_items': items_list, # Filtered items for the table
            'selected_category': selected_category,
            'selected_year_str': selected_year_str, # For dropdown state ('latest', 'All', or year)
            'selected_year': selected_year, # Actual year int or None
            'available_years': available_years,
        })
        # Remove old debug prints if they were here
        # print(f"--- DEBUG en_page(news-room): Press Releases Count: ...")

    # --- Keep existing logic for other pages like index, results-center ---
    if page_name == 'index':
        # Add financial highlights data for the homepage
        financial_highlights_data = get_financial_highlights_data(language)
        context['financial_highlights'] = financial_highlights_data['highlights']
        context['latest_earnings'] = financial_highlights_data['latest_earnings']
        
        # Add latest news for the homepage
        context['latest_news'] = News.objects.all().order_by('-publish_date')[:3]
        
        # Add newsroom article data for the homepage  
        context['newsroom_article'] = NewsroomArticle.objects.all().order_by('-date').first()
        
        # Get active years directly from the database
        active_years = Year.objects.filter(is_active=True).order_by('-year')
        context['years'] = [y.year for y in active_years] # Pass list of year numbers
        
        # Get all earnings results
        all_results = EarningsResult.objects.all().order_by('-year', 'period')
        result_years = sorted(list(set(all_results.values_list('year', flat=True))), reverse=True)
        context['years'] = result_years

        # Group results by year using the determined years list
        context['earnings_results_by_year'] = {}
        for year_val in context['years']:
            context['earnings_results_by_year'][year_val] = all_results.filter(year=year_val)
        
        # Get shareholders data
        latest_structure = ShareholdingStructure.objects.filter(
            language=language
        ).order_by('-year', '-period').first()
        
        if latest_structure:
            shareholders = latest_structure.shareholders.all()
            shareholders_json = json.dumps([{"name": s.name, "percentage": float(s.percentage)} for s in shareholders])
            context['shareholders'] = shareholders
            context['shareholders_json'] = shareholders_json
        else:
            # Default data if no shareholders exist
            default_shareholders_json = json.dumps([
                {"name": "Major Shareholders", "percentage": 70.0},
                {"name": "Free Float", "percentage": 30.0}
            ])
            context['shareholders'] = []
            context['shareholders_json'] = default_shareholders_json
    if page_name == 'results-center':
        # ... (results-center logic remains) ...
        pass
    # --- End of other page logic ---

    return render(request, template_path, context)

def ar_page(request, page_name=None):
    # Template path logic...
    if page_name is None:
        template_path = os.path.join('website', 'ir.gb-corporation.com', 'ar', 'index.html') # Example default
        page_name = 'index' # Set page_name for context fetching
    else:
        # Check if page_name already has .html extension
        if page_name.endswith('.html'):
            template_path = os.path.join('website', 'ir.gb-corporation.com', 'ar', page_name)
        else:
            template_path = os.path.join('website', 'ir.gb-corporation.com', 'ar', f'{page_name}.html')

    language = 'ar'
    context = get_common_context(language)
    # Fetch general page context first
    context.update(get_page_context(request, page_name, language))

    # Add specific context for the "filings" page (Arabic)
    if page_name == 'filings':
        print(f"--- DEBUG (ar): Inside filings context block ---") # DEBUG
        try:
            latest_shortcut = FundamentalsShortcut.objects.filter(language=language).order_by('-year', '-period').first()
            print(f"--- DEBUG (ar): Fetched latest_shortcut: {latest_shortcut} ---") # DEBUG
            if latest_shortcut:
                 print(f"--- DEBUG (ar): Found latest_shortcut: {latest_shortcut} ---") # DEBUG
            context['fundamentals_shortcut'] = latest_shortcut
        except Exception as e:
            print(f"--- DEBUG (ar): Error fetching shortcut: {e} ---") # DEBUG
            context['fundamentals_shortcut'] = None
            
        try:
            latest_annual_report = AnnualReport.objects.filter(language=language, is_active=True).order_by('-year').first()
            context['annual_reports_url'] = latest_annual_report.pdf_file.url if latest_annual_report and latest_annual_report.pdf_file else None
        except AnnualReport.DoesNotExist:
            context['annual_reports_url'] = None

        # Add context for the financial data tabs (Snapshot, IS, BS) - Arabic
        try:
            # Get all fundamental data for the snapshot table (multiple periods)
            context['financial_data'] = Fundamentals.objects.filter(language=language, is_active=True).order_by('-date')[:4] # Get latest 4 for snapshot
            # Get the single latest fundamental data for IS and BS tabs
            context['latest_fundamental'] = Fundamentals.objects.filter(language=language, is_active=True).order_by('-date').first()
        except Fundamentals.DoesNotExist:
            context['financial_data'] = []
            context['latest_fundamental'] = None
            
        # Add context for Dividends table (assuming dividends are language-agnostic or handled by model)
        context['dividends'] = Dividend.objects.filter(is_active=True).order_by('-payment_date')
        
        # Add context for Factsheets (grouped by year) - AR
        # Assuming Factsheet title/title_ar is used for display
        all_factsheets_ar = Factsheet.objects.all().order_by('-year__year', '-period')
        factsheet_years_ar = sorted(list(set(all_factsheets_ar.values_list('year__year', flat=True))), reverse=True)
        factsheets_by_year_ar = {year: [] for year in factsheet_years_ar}
        for fs in all_factsheets_ar:
             if fs.year.year in factsheets_by_year_ar:
                 factsheets_by_year_ar[fs.year.year].append(fs)

        context['factsheet_years'] = factsheet_years_ar # Use the same key name as EN
        context['factsheets_by_year'] = factsheets_by_year_ar
        
        # Add the filings data and pagination setup - ADD THIS SECTION FOR AR
        from django.core.paginator import Paginator
        
        # Get filing type options (same as EN version)
        filing_types = [
            ('annual-reports', 'التقارير السنوية'),
            ('quarterly-reports', 'التقارير الربع سنوية'),
            ('earnings-releases', 'إفصاحات الأرباح'),
            ('investor-presentations', 'عروض المستثمرين'),
            ('analyst-toolkit', 'أدوات المحللين'),
            ('corporate-documents', 'وثائق الشركة')
        ]
        context['filing_types'] = filing_types
        
        # Get active years for filtering
        active_years = Year.objects.filter(is_active=True).order_by('-year')
        years_list = [y.year for y in active_years]
        context['years'] = years_list
        
        # Get selected filters from request
        selected_type = request.GET.get('type', '')
        selected_year = request.GET.get('year', '')
        context['selected_type'] = selected_type
        context['selected_year'] = selected_year
        
        # Build the filings query
        from django.db.models import Q
        filing_query = Q(language=language, is_active=True)
        
        # Apply type filter if selected
        if selected_type:
            filing_query &= Q(type=selected_type)
        
        # Apply year filter if selected
        if selected_year:
            filing_query &= Q(year=selected_year)
        
        # Fetch filings based on query
        all_filings = Document.objects.filter(filing_query).order_by('-date')
        context['all_filings'] = all_filings
        
        # Set up pagination
        page_num = request.GET.get('page', 1)
        try:
            page_num = int(page_num)
        except ValueError:
            page_num = 1
        
        paginator = Paginator(all_filings, 10)  # 10 filings per page
        filings = paginator.get_page(page_num)
        
        context['filings'] = filings
        context['paginator'] = paginator
        context['page_num'] = page_num
        
        print(f"--- DEBUG (ar): Loaded {all_filings.count()} filings, page {page_num} of {paginator.num_pages} ---")

    # Add specific context for news-room page
    if page_name == 'news-room':
        selected_category = request.GET.get('category', 'press_releases')
        selected_year_str = request.GET.get('year', 'All') # Changed default from 'latest' to 'All'

        # Get available years for filters
        press_release_years = PressRelease.objects.filter(language=language, is_active=True).values_list('publish_date__year', flat=True).distinct()
        disclosure_years = CorporateDisclosure.objects.filter(language=language, is_active=True).values_list('disclosure_date__year', flat=True).distinct()
        available_years = sorted(list(set(list(press_release_years) + list(disclosure_years))), reverse=True)

        # Determine selected year
        latest_year = available_years[0] if available_years else None
        if selected_year_str == 'latest':
            selected_year = latest_year
        elif selected_year_str == 'All':
            selected_year = None # No year filter
        else:
            try:
                selected_year = int(selected_year_str)
                if selected_year not in available_years: # Validate selected year exists
                    selected_year = latest_year
                    selected_year_str = 'latest' # Reset str if invalid year requested
            except (ValueError, TypeError):
                selected_year = latest_year
                selected_year_str = 'latest' # Reset str if invalid

        # Fetch data based on category and year
        items_list = []
        if selected_category == 'press_releases':
            query = PressRelease.objects.filter(language=language, is_active=True)
            if selected_year:
                query = query.filter(publish_date__year=selected_year)
            releases = query.order_by('-publish_date')
            for item in releases:
                file_url = item.file.url if item.file else '#'
                items_list.append({
                    'type': 'press_release',
                    'title': item.title, # Assuming title field holds Arabic text
                    'date': item.publish_date,
                    'url': file_url,
                    'format': 'PDF'
                })
        elif selected_category == 'disclosures':
            query = CorporateDisclosure.objects.filter(language=language, is_active=True)
            if selected_year:
                query = query.filter(disclosure_date__year=selected_year)
            disclosures = query.order_by('-disclosure_date')
            for item in disclosures:
                file_url = item.document.url if item.document else '#'
                items_list.append({
                    'type': 'disclosure',
                    'title': item.title, # Assuming title field holds Arabic text
                    'date': item.disclosure_date,
                    'url': file_url,
                    'format': 'PDF' if item.document else 'N/A'
                })

        # Keep Newsroom Articles separate for top section
        newsroom_articles_qs = NewsroomArticle.objects.all().order_by('-date')
        newsroom_articles_list = list(newsroom_articles_qs)
        # Use title_ar and summary_ar for Arabic news articles
        for article in newsroom_articles_list:
            article.display_title = article.title_ar
            article.display_summary = article.summary_ar
        print(f"--- DEBUG news_room_ar: Newsroom Articles Count: {len(newsroom_articles_list)}")


        # Update context for news-room specifically
        context.update({
            'page_title': 'غرفة الأخبار',
            'newsroom_articles': newsroom_articles_list, # For top section
            'press_release_items': items_list, # Filtered items for the table
            'selected_category': selected_category,
            'selected_year_str': selected_year_str, # For dropdown state ('latest', 'All', or year)
            'selected_year': selected_year, # Actual year int or None
            'available_years': available_years,
        })
        # Remove old debug prints if they were here
        # print(f"--- DEBUG ar_page(news-room): Press Releases Count: ...")


    # --- Keep existing logic for other pages like index, results-center ---
    if page_name == 'index':
        # Add financial highlights data for the homepage
        financial_highlights_data = get_financial_highlights_data(language)
        context['financial_highlights'] = financial_highlights_data['highlights']
        context['latest_earnings'] = financial_highlights_data['latest_earnings']
        
        # Add latest news for the homepage
        context['latest_news'] = News.objects.all().order_by('-publish_date')[:3]
        
        # Add newsroom article data for the homepage  
        context['newsroom_article'] = NewsroomArticle.objects.all().order_by('-date').first()
        
        # Get active years directly from the database
        active_years = Year.objects.filter(is_active=True).order_by('-year')
        context['years'] = [y.year for y in active_years] # Pass list of year numbers
        
        # Get all earnings results
        all_results = EarningsResult.objects.all().order_by('-year', 'period')
        result_years = sorted(list(set(all_results.values_list('year', flat=True))), reverse=True)
        context['years'] = result_years

        # Group results by year using the determined years list
        context['earnings_results_by_year'] = {}
        for year_val in context['years']:
            context['earnings_results_by_year'][year_val] = all_results.filter(year=year_val)
        
        # Get shareholders data
        latest_structure = ShareholdingStructure.objects.filter(
            language=language
        ).order_by('-year', '-period').first()
        
        if latest_structure:
            shareholders = latest_structure.shareholders.all()
            shareholders_json = json.dumps([{"name": s.name, "percentage": float(s.percentage)} for s in shareholders])
            context['shareholders'] = shareholders
            context['shareholders_json'] = shareholders_json
        else:
            # Default data if no shareholders exist
            default_shareholders_json = json.dumps([
                {"name": "كبار المساهمين", "percentage": 70.0},
                {"name": "التداول الحر", "percentage": 30.0}
            ])
            context['shareholders'] = []
            context['shareholders_json'] = default_shareholders_json
    if page_name == 'results-center':
        # ... (results-center logic remains) ...
        pass
    # --- End of other page logic ---

    return render(request, template_path, context)

def get_board_and_management_data(language='en'):
    # Get board members and management team
    board_members = BoardMember.objects.filter(
        language=language,
        is_active=True
    ).order_by('order')

    # Separate board members and management based on member_type
    board_of_directors = board_members.filter(member_type='board')
    management_team = board_members.filter(member_type='management')

    # Group management team by section
    management_by_section = {}
    for member in management_team:
        section = member.section or 'Other'  # Use 'Other' if no section specified
        if section not in management_by_section:
            management_by_section[section] = []
        management_by_section[section].append(member)

    return {
        'board_of_directors': board_of_directors,
        'management_team': management_team,
        'management_by_section': management_by_section,
        'page_title': 'Board and Management' if language == 'en' else 'مجلس الإدارة والإدارة التنفيذية'
    }

def get_corporate_governance_data(language='en'):
    # Get all corporate governance items for the language
    governance_items = CorporateGovernance.objects.filter(
        language=language,
        is_active=True
    ).order_by('document_type', 'title')
    
    # Group items by document type
    grouped_items = {
        'board': [],
        'committee': [],
        'policy': [],
        'control': []
    }
    
    for item in governance_items:
        if item.document_type in grouped_items:
            grouped_items[item.document_type].append(item)
    
    return {
        'governance_items': grouped_items,
        'page_title': 'Corporate Governance' if language == 'en' else 'نظام الحوكمة'
    }

def get_at_a_glance_data(language='en'):
    # Get overview text
    try:
        overview = AtAGlanceOverview.objects.get(language=language)
    except AtAGlanceOverview.DoesNotExist:
        overview = None

    # Get all business segments ordered by revenue percentage
    segments = BusinessSegmentOverview.objects.filter(
        language=language
    ).order_by('-revenue_percentage')

    # Get key points and brands for each segment
    segments_data = []
    for segment in segments:
        key_points = BusinessSegmentKeyPoint.objects.filter(
            segment=segment
        ).order_by('order')
        
        brands = BusinessSegmentBrand.objects.filter(
            segment=segment
        ).order_by('order')
        
        segments_data.append({
            'segment': segment,
            'key_points': key_points,
            'brands': brands
        })

    return {
        'overview': overview,
        'segments': segments_data,
        'page_title': 'At a Glance' if language == 'en' else 'نظرة سريعة'
    }

def get_company_profile_context(language='en'):
    """Get context data for company profile pages"""
    return {
        'company_profiles': CompanyProfile.objects.filter(
            language=language,
            is_active=True
        ),
        'business_segments': BusinessSegment.objects.filter(
            language=language,
            is_active=True
        ).order_by('segment_type'),
        'vision_mission': CompanyVisionMission.objects.filter(
            language=language,
            is_active=True
        ),
        'core_values': CompanyCoreValue.objects.filter(
            language=language,
            is_active=True
        ).order_by('order'),
        'page_title': 'Company Profile' if language == 'en' else 'عن شركة جي بي أوتو'
    }

def get_strategy_overview_data(language='en'):
    """Get strategy overview data from database"""
    strategies = Strategy.objects.filter(
        language=language,
        is_active=True
    ).order_by('category')
    
    # Organize strategies by category
    strategy_data = {
        'overall': None,
        'gb_auto': None,
        'gb_capital': None
    }
    
    for strategy in strategies:
        strategy_data[strategy.category] = {
            'title': strategy.title,
            'description': strategy.description,
            'objectives': json.loads(strategy.objectives),
            'pillars': json.loads(strategy.pillars),
            'files': json.loads(strategy.files),
            'links': json.loads(strategy.links)
        }
    
    return {
        'strategies': strategy_data,
        'page_title': 'Strategy Overview' if language == 'en' else 'نظرة عامة على الاستراتيجية'
    }

def get_ir_contacts_data(language='en'):
    """Get data for ir-contacts page"""
    
    context = {}
    
    # Get hero sections for the contact page
    context['hero_sections'] = get_hero_sections(page='contact', language=language)
    
    # Get IR contacts ordered by their defined order
    contacts = IRContact.objects.filter(
        is_active=True
    ).order_by('order')

    # Get office location
    try:
        office = IROfficeLocation.objects.filter(is_active=True).first()
    except IROfficeLocation.DoesNotExist:
        office = None

    context['contacts'] = contacts
    context['office'] = office
    context['page_title'] = 'IR Contacts' if language == 'en' else 'إدارة علاقات المستثمرين'
    
    return context

def get_our_values_data(language='en'):
    """Get data for our-values page"""
    
    # Get hero sections for the about page with the correct language
    try:
        hero_sections = HeroSection.objects.filter(
            page='about',
            language=language,
            is_active=True
        ).order_by('order')
        print(f"Hero sections for about page ({language}): {hero_sections.count()}")
    except Exception as e:
        print(f"Error retrieving hero sections: {e}")
        hero_sections = []
    
    # Get company overview data
    try:
        company_overview = CompanyOverview.objects.filter(
            language=language,
            is_active=True
        ).first()
        print(f"CompanyOverview for {language}: {company_overview}")
        
        # Debug output for company overview data
        if company_overview:
            print(f"Vision: {company_overview.vision}")
            print(f"Mission: {company_overview.mission}")
    except Exception as e:
        print(f"Error retrieving company overview: {e}")
        company_overview = None
    
    # Get business segments data
    try:
        business_segments = BusinessSegmentOverview.objects.filter(
            language=language,
            is_active=True
        ).order_by('order')
        print(f"BusinessSegments for {language}: {business_segments.count()}")
    except Exception as e:
        print(f"Error retrieving business segments: {e}")
        business_segments = []
    
    # Get core values data
    try:
        core_values = CompanyCoreValue.objects.filter(
            language=language,
            is_active=True
        ).order_by('order')
        print(f"CoreValues for {language}: {core_values.count()}")
    except Exception as e:
        print(f"Error retrieving core values: {e}")
        core_values = []
    
    # Get timeline data with explicit handling
    try:
        timeline = HistoryTimeline.objects.filter(
            language=language
        ).order_by('-year')
        print(f"Timeline entries for {language}: {timeline.count()}")
        
        # Debug for timeline years
        years = [t.year for t in timeline]
        print(f"Timeline years: {years}")
        
        # Process timeline data
        timeline_years = []
        active_year_events = []
        
        if timeline.exists():
            # Set the first year as active by default
            active_year = timeline.first().year
            print(f"Active year: {active_year}")
            
            # Create year markers for all timeline entries
            for entry in timeline:
                timeline_years.append({
                    'year': entry.year,
                    'active': entry.year == active_year
                })
                
                # Get events for active year
                if entry.year == active_year:
                    # Check the data type and structure of events
                    print(f"Events data type: {type(entry.events)}")
                    print(f"Events raw value: {entry.events}")
                    
                    # Handle different data types for events
                    if isinstance(entry.events, list):
                        active_year_events = entry.events
                    elif isinstance(entry.events, str):
                        # If it's a string that looks like a JSON array, try to parse it
                        if entry.events.startswith('[') and entry.events.endswith(']'):
                            try:
                                import json
                                active_year_events = json.loads(entry.events)
                            except:
                                active_year_events = [entry.events]
                        else:
                            active_year_events = [entry.events]
                    elif entry.events:
                        # Any other non-empty value
                        active_year_events = [str(entry.events)]
                    else:
                        active_year_events = []
                    
                    print(f"Active year events count: {len(active_year_events)}")
                    for idx, event in enumerate(active_year_events[:3]):
                        print(f"Event {idx+1}: {event[:50]}...")
        else:
            print("No timeline entries found")
            
    except Exception as e:
        print(f"Error retrieving timeline data: {e}")
        import traceback
        traceback.print_exc()
        timeline_years = []
        active_year_events = []
        
    # Return all the data for our-values page
    print(f"Returning timeline_years: {timeline_years}")
    print(f"Returning active_year_events: {active_year_events}")
    
    return {
        'hero_sections': hero_sections,
        'company_overview': company_overview,
        'business_segments': business_segments,
        'core_values': core_values,
        'timeline_years': timeline_years,
        'active_year_events': active_year_events,
        'page_title': 'Our Values' if language == 'en' else 'قيمنا'
    }

def get_financial_highlights_data(language='en'):
    """
    Get financial highlights data for the homepage
    """
    # Get the latest financial highlight data
    try:
        latest_highlight = FinancialHighlight.objects.filter(
            language=language,
            is_active=True
        ).order_by('-year', '-period').first()
        
        if latest_highlight:
            highlights = {
                'revenues': latest_highlight.revenues,
                'gross_profit': latest_highlight.gross_profit,
                'net_income': latest_highlight.net_income,
                'period_display': latest_highlight.period_display
            }
        else:
            # Empty fallback - let template handle missing data gracefully
            highlights = {
                'revenues': Decimal('0'),
                'gross_profit': Decimal('0'),
                'net_income': Decimal('0'),
                'period_display': "No Data Available" if language == 'en' else "لا توجد بيانات متاحة"
            }
    except Exception as e:
        # Empty fallback - let template handle missing data gracefully
        highlights = {
            'revenues': Decimal('0'),
            'gross_profit': Decimal('0'),
            'net_income': Decimal('0'),
            'period_display': "No Data Available" if language == 'en' else "لا توجد بيانات متاحة"
        }
    
    # Get the latest earnings results for homepage display
    latest_earnings = EarningsResult.objects.all().order_by('-year', '-period').first()
    
    return {
        'highlights': highlights,
        'latest_earnings': latest_earnings
    }

def get_page_context(request, page_name, language='en'):
    """Get context data for a specific page"""
    context = {}
    
    # Add hero sections for this page
    page_mapping = {
        'about': 'about',
        'corporate-governance': 'governance',
        'esg': 'esg',
        'ir': 'ir',
        'news': 'news',
        'contact': 'contact',
        'leadership-and-governance': 'governance',
        'news-room': 'news',
        'ir-contacts': 'contact',
        'share-information': 'ir',
        'filings': 'ir',
        'corporate-information': 'ir',
        'investment-calculator': 'ir',
    }
    
    # Map the page_name to the hero section page value
    hero_page = page_mapping.get(page_name, 'other')
    context['hero_sections'] = get_hero_sections(page=hero_page, language=language)
    
    # Handle specific pages with custom context
    if page_name == 'board-and-management':
        context.update(get_board_and_management_data(language))
    elif page_name == 'corporate-governance':
        context.update(get_corporate_governance_data(language))
    elif page_name == 'at-a-glance':
        context.update(get_at_a_glance_data(language))
    elif page_name == 'company-profile':
        context.update(get_company_profile_context(language))
    elif page_name == 'strategy-overview':
        context.update(get_strategy_overview_data(language))
    elif page_name == 'contact-ir':
        context.update(get_ir_contacts_data(language))
    elif page_name == 'ir-contacts':
        context.update(get_ir_contacts_data(language))
    elif page_name == 'our-values':
        context.update(get_our_values_data(language))
    elif page_name == 'financial-highlights':
        context.update(get_financial_highlights_data(language))
    
    return context

# API Endpoints
from core.security_decorators import enhanced_security
from core.throttling import LegacySubscriptionThrottle
from django.views.decorators.csrf import csrf_exempt

@csrf_exempt
@enhanced_security(throttle_class=LegacySubscriptionThrottle, require_post=True, validate_email=True)
def subscribe(request):
    """
    Legacy subscription endpoint with enhanced security
    """
    try:
        email = request.POST.get('email', '').strip()
        preferences = request.POST.getlist('preferences')

        # Additional validation
        if not email:
            return JsonResponse({
                'success': False,
                'status': 'error',
                'message': 'Email is required'
            }, status=400)

        if not preferences:
            return JsonResponse({
                'success': False,
                'status': 'error',
                'message': 'At least one preference is required'
            }, status=400)

        # Validate preferences
        valid_preferences = ['newsletter', 'updates', 'alerts', 'reports']
        invalid_prefs = [pref for pref in preferences if pref not in valid_preferences]
        if invalid_prefs:
            return JsonResponse({
                'success': False,
                'status': 'error',
                'message': f'Invalid preferences: {", ".join(invalid_prefs)}'
            }, status=400)

        # Create subscriber
        subscriber, created = Subscriber.objects.get_or_create(email=email)

        # Add preferences
        for pref in preferences:
            SubscriptionPreference.objects.get_or_create(
                subscriber=subscriber,
                preference_type=pref
            )

        # Log successful subscription
        from core.security_monitoring import security_monitor, SecurityEventType
        from api.tools import get_client_ip

        security_monitor.log_security_event(
            SecurityEventType.SUBSCRIPTION_SUCCESS,
            email,
            get_client_ip(request),
            {
                'preferences': preferences,
                'endpoint': 'legacy_subscribe',
                'created_new': created
            },
            'low'
        )

        return JsonResponse({
            'success': True,
            'status': 'success',
            'message': 'Subscription successful'
        })

    except Exception as e:
        logger.error(f"Legacy subscription error: {str(e)}")
        return JsonResponse({
            'success': False,
            'status': 'error',
            'message': 'An error occurred during subscription'
        }, status=500)

def stock_price_history(request):
    days = int(request.GET.get('days', 30))
    prices = StockPrice.objects.all()[:days]
    data = [{
        'date': price.date.isoformat(),
        'close': float(price.close_price),
        'volume': price.volume
    } for price in prices]
    return JsonResponse({'prices': data})

from core.throttling import StockAlertThrottle

@csrf_exempt
@enhanced_security(throttle_class=StockAlertThrottle, require_post=True, validate_email=True)
def create_stock_alert(request):
    """
    Create stock alert with enhanced security
    """
    try:
        email = request.POST.get('email', '').strip()
        alert_type = request.POST.get('alert_type', '').strip()
        threshold = request.POST.get('threshold', '').strip()

        # Validation
        if not email:
            return JsonResponse({
                'success': False,
                'status': 'error',
                'message': 'Email is required'
            }, status=400)

        if not alert_type:
            return JsonResponse({
                'success': False,
                'status': 'error',
                'message': 'Alert type is required'
            }, status=400)

        if not threshold:
            return JsonResponse({
                'success': False,
                'status': 'error',
                'message': 'Threshold value is required'
            }, status=400)

        # Validate alert type
        valid_alert_types = ['price_above', 'price_below', 'volume_above', 'change_percent']
        if alert_type not in valid_alert_types:
            return JsonResponse({
                'success': False,
                'status': 'error',
                'message': f'Invalid alert type. Must be one of: {", ".join(valid_alert_types)}'
            }, status=400)

        # Validate threshold value
        try:
            threshold_value = float(threshold)
            if threshold_value <= 0:
                raise ValueError("Threshold must be positive")
        except (ValueError, TypeError):
            return JsonResponse({
                'success': False,
                'status': 'error',
                'message': 'Threshold must be a positive number'
            }, status=400)

        # Create subscriber
        subscriber, created = Subscriber.objects.get_or_create(email=email)

        # Create alert
        alert = StockAlert.objects.create(
            subscriber=subscriber,
            alert_type=alert_type,
            threshold_value=threshold_value
        )

        # Log successful alert creation
        from core.security_monitoring import security_monitor, SecurityEventType
        from api.tools import get_client_ip

        security_monitor.log_security_event(
            SecurityEventType.STOCK_ALERT_CREATED,
            email,
            get_client_ip(request),
            {
                'alert_type': alert_type,
                'threshold_value': threshold_value,
                'alert_id': alert.id if hasattr(alert, 'id') else None,
                'created_new_subscriber': created
            },
            'low'
        )

        return JsonResponse({
            'success': True,
            'status': 'success',
            'message': 'Stock alert created successfully',
            'alert_id': alert.id if hasattr(alert, 'id') else None
        })

    except Exception as e:
        logger.error(f"Stock alert creation error: {str(e)}")
        return JsonResponse({
            'success': False,
            'status': 'error',
            'message': 'An error occurred while creating the alert'
        }, status=500)

def calendar_events(request):
    language = request.POST.get('_lang', 'en')
    events = IRCalendarEvent.objects.filter(
        language=language,
        is_active=True
    ).order_by('-start_date')
    
    events_data = [{
        'start': event.start_date.strftime('%Y-%m-%d'),
        'end': event.end_date.strftime('%Y-%m-%d') if event.end_date else event.start_date.strftime('%Y-%m-%d'),
        'title': event.title,
        'url': event.virtual_link or '#'
    } for event in events]
    
    return JsonResponse(events_data, safe=False)

def esg_policy(request, language):
    # Get common context first
    context = get_common_context(language)
    
    # Get ESG policies filtered by language and category
    policies = ESGPolicy.objects.filter(
        is_active=True,
        language=language,
        category='purpose'  # Start with purpose category
    ).prefetch_related(
        'pillars',
        'pillars__commitments'
    ).order_by('order')

    # Debug information
    print(f"Language: {language}")
    print(f"Number of policies found: {policies.count()}")
    for policy in policies:
        print(f"Policy: {policy.title} ({policy.language})")
        print(f"Number of pillars: {policy.pillars.count()}")
        print(f"Content preview: {policy.content[:100]}")

    # If no policies found, try to load them
    if not policies.exists():
        from django.core.management import call_command
        print("No policies found. Loading data...")
        call_command('load_esg_policy_data')
        # Try to get policies again
        policies = ESGPolicy.objects.filter(
            is_active=True,
            language=language,
            category='purpose'
        ).prefetch_related(
            'pillars',
            'pillars__commitments'
        ).order_by('order')
        print(f"After loading, number of policies: {policies.count()}")

    # Update context with policies
    context.update({
        'policies': policies,
        'language': language,
    })
    
    template_name = f'website/ir.gb-corporation.com/{language}/esg-policy.html'
    return render(request, template_name, context)

def share_information(request, lang='en'):
    """Share information page with current stock data"""
    # Get common context first
    context = get_common_context(lang)
    
    # Add hero sections for IR page
    context['hero_sections'] = get_hero_sections(page='ir', language=lang)
    
    template_path = f'website/ir.gb-corporation.com/{lang}/share-information.html'
    
    try:
        logger.info(f"Processing share information request for language: {lang}")
    except Exception as log_error:
        pass
    
    # Remove redundant context initialization
    # context = get_common_context(lang)  # This line is removed to prevent overwriting hero_sections
    
    try:
        # Initialize StockDataService
        try:
            logger.debug("Initializing StockDataService")
        except Exception:
            pass
            
        stock_service = StockDataService()
        
        # Get real-time stock data with force refresh if requested
        force_refresh = request.GET.get('refresh', '').lower() == 'true'
        try:
            logger.debug("Fetching real-time stock data")
        except Exception:
            pass
            
        stock_data = stock_service.get_delayed_stock_data(force_refresh=force_refresh)
        
        try:
            logger.info(f"Successfully retrieved stock data: {stock_data}")
        except Exception:
            pass
        
        # Get historical data for the chart
        try:
            logger.debug("Fetching historical data")
        except Exception:
            pass
            
        historical_data = stock_service.get_historical_data()
        
        try:
            logger.info(f"Retrieved {len(historical_data)} historical data points")
        except Exception:
            pass
        
        # Format date based on language
        if lang == 'ar':
            # Convert English month abbreviation to Arabic
            month_map = {
                'JAN': 'يناير', 'FEB': 'فبراير', 'MAR': 'مارس',
                'APR': 'أبريل', 'MAY': 'مايو', 'JUN': 'يونيو',
                'JUL': 'يوليو', 'AUG': 'أغسطس', 'SEP': 'سبتمبر',
                'OCT': 'أكتوبر', 'NOV': 'نوفمبر', 'DEC': 'ديسمبر'
            }
            last_trade_time = stock_data['last_trade_time'].strftime('%d-%b-%y %H:%M').upper()
            for eng, ar in month_map.items():
                last_trade_time = last_trade_time.replace(eng, ar)
        else:
            last_trade_time = stock_data['last_trade_time'].strftime('%d-%b-%y %H:%M').upper()
        
        # Get 52-week high/low values
        yearly_data = get_52_week_high_low()
        
        # Prepare stock data for template/JSON response
        formatted_stock_data = {
            'last_trade_time': last_trade_time,
            'last_trade_price': stock_data['display']['last_trade_price'],
            'net_last_change': stock_data['display']['net_last_change'],
            'last_change_percentage': stock_data['display']['last_change_percentage'],
            'today_high_price': stock_data['display']['today_high_price'],
            'today_low_price': stock_data['display']['today_low_price'],
            'today_open_price': stock_data['display']['today_open_price'],
            'today_close_price': stock_data['display']['last_trade_price'],
            'today_total_volume': stock_data['display']['today_total_volume'],
            'today_total_value': stock_data['display']['today_total_value'],
            'today_total_trades': f"{stock_data['today_total_trades']:,}",
            'year_high_price': f"{yearly_data['year_high_price']:.2f}",
            'year_low_price': f"{yearly_data['year_low_price']:.2f}",
            'data_source': 'live'
        }
        
        # If it's an AJAX request, return JSON response
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'stock_data': formatted_stock_data,
                'historical_data': json.dumps(historical_data)
            })
        
        # Otherwise, update context for template rendering
        context.update(formatted_stock_data)
        context['historical_data'] = json.dumps(historical_data)
        
    except Exception as e:
        logger.error(f"Error fetching live stock data: {str(e)}", exc_info=True)
        # Get the latest records from StockHistory as fallback
        try:
            # Get latest record for current data
            latest_stock = StockHistory.objects.order_by('-date', '-last_update_time').first()
            
            if not latest_stock:
                logger.error("No historical stock data available in database")
                context.update({
                    'error_message': 'لا تتوفر بيانات السهم.' if lang == 'ar' else 'No stock data available.',
                    'data_source': 'none'
                })
                return render(request, template_path, context)
                
            # Format the date based on language
            if lang == 'ar':
                formatted_time = latest_stock.date.strftime('%d-%b-%y').upper()
                # Convert English month abbreviation to Arabic
                month_map = {
                    'JAN': 'يناير', 'FEB': 'فبراير', 'MAR': 'مارس',
                    'APR': 'أبريل', 'MAY': 'مايو', 'JUN': 'يونيو',
                    'JUL': 'يوليو', 'AUG': 'أغسطس', 'SEP': 'سبتمبر',
                    'OCT': 'أكتوبر', 'NOV': 'نوفمبر', 'DEC': 'ديسمبر'
                }
                for eng, ar in month_map.items():
                    formatted_time = formatted_time.replace(eng, ar)
                if latest_stock.last_update_time:
                    formatted_time += f" {latest_stock.last_update_time.strftime('%H:%M')}"
            else:
                formatted_time = latest_stock.date.strftime('%d-%b-%y').upper()
                if latest_stock.last_update_time:
                    formatted_time += f" {latest_stock.last_update_time.strftime('%H:%M')}"
            
            # Get 52-week high/low values for fallback data
            yearly_data = get_52_week_high_low()
            
            # Prepare fallback data
            fallback_data = {
                'last_trade_time': formatted_time,
                'last_trade_price': f"{latest_stock.close_price:.2f}",
                'net_last_change': latest_stock.change,
                'last_change_percentage': latest_stock.change_percent,
                'today_high_price': f"{latest_stock.high_price:.2f}",
                'today_low_price': f"{latest_stock.low_price:.2f}",
                'today_open_price': f"{latest_stock.open_price:.2f}",
                'today_close_price': f"{latest_stock.close_price:.2f}",
                'today_total_volume': f"{latest_stock.volume:,}",
                'today_total_value': f"{latest_stock.turnover:,.2f}" if hasattr(latest_stock, 'turnover') and latest_stock.turnover else "N/A",
                'today_total_trades': "N/A",
                'year_high_price': f"{yearly_data['year_high_price']:.2f}",
                'year_low_price': f"{yearly_data['year_low_price']:.2f}",
                'data_source': 'historical'
            }
            
            # If it's an AJAX request, return JSON response
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'stock_data': fallback_data,
                    'historical_data': json.dumps(historical_data) if 'historical_data' in locals() else None
                })
            
            # Otherwise, update context for template rendering
            context.update(fallback_data)
            
        except Exception as inner_e:
            logger.error(f"Error fetching historical data: {str(inner_e)}", exc_info=True)
            error_data = {
                'error_message': 'تعذر جلب بيانات السهم في الوقت الحالي.' if lang == 'ar' else 'Unable to fetch any stock data at this time.',
                'data_source': 'none'
            }
            
            # If it's an AJAX request, return JSON response
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse(error_data)
            
            # Otherwise, update context for template rendering
            context.update(error_data)
    
    return render(request, template_path, context)

from core.throttling import StockHistoryThrottle
from core.security_decorators import basic_security
from django.core.paginator import Paginator

@basic_security(throttle_class=StockHistoryThrottle, require_post=False)
def stock_history_api(request):
    """
    Stock history API with pagination and throttling to prevent data scraping
    """
    try:
        # Check if this is a chart request (needs all data)
        is_chart_request = request.GET.get('chart', 'false').lower() == 'true'

        # Get pagination parameters
        page = int(request.GET.get('page', 1))
        page_size = min(int(request.GET.get('page_size', 100)), 500)  # Max 500 records per page

        # For chart requests, allow larger datasets but still limit to prevent abuse
        if is_chart_request:
            page_size = min(int(request.GET.get('page_size', 1000)), 2000)  # Max 2000 for charts

        # Get date range filters if provided
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')

        # Build query
        history_query = StockHistory.objects.all().order_by('-date')  # Most recent first

        # Apply date filters if provided
        if start_date:
            try:
                from datetime import datetime
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                history_query = history_query.filter(date__gte=start_date_obj)
            except ValueError:
                return JsonResponse({
                    'success': False,
                    'status': 'error',
                    'message': 'Invalid start_date format. Use YYYY-MM-DD'
                }, status=400)

        if end_date:
            try:
                from datetime import datetime
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
                history_query = history_query.filter(date__lte=end_date_obj)
            except ValueError:
                return JsonResponse({
                    'success': False,
                    'status': 'error',
                    'message': 'Invalid end_date format. Use YYYY-MM-DD'
                }, status=400)

        # Apply pagination
        paginator = Paginator(history_query, page_size)

        if page > paginator.num_pages:
            return JsonResponse({
                'success': False,
                'status': 'error',
                'message': f'Page {page} does not exist. Total pages: {paginator.num_pages}'
            }, status=404)

        page_obj = paginator.get_page(page)

        # Convert to list of dictionaries
        data = list(page_obj.object_list.values(
            'date', 'day_of_week', 'open_price', 'close_price',
            'high_price', 'low_price', 'volume', 'change', 'change_percent'
        ))

        # Format dates to ISO format for JSON
        for item in data:
            item['date'] = item['date'].isoformat()

        # Log API access for monitoring
        from core.security_monitoring import security_monitor, SecurityEventType
        from api.tools import get_client_ip

        security_monitor.log_security_event(
            SecurityEventType.SUBSCRIPTION_SUCCESS,  # Reusing for API access
            'anonymous',
            get_client_ip(request),
            {
                'endpoint': 'stock_history_api',
                'page': page,
                'page_size': page_size,
                'total_records': len(data),
                'is_chart_request': is_chart_request,
                'filters': {
                    'start_date': start_date,
                    'end_date': end_date
                }
            },
            'low'
        )

        # For chart requests, return data in legacy format for compatibility
        if is_chart_request:
            return JsonResponse(data, safe=False)

        # For regular API requests, return paginated format
        return JsonResponse({
            'success': True,
            'data': data,
            'pagination': {
                'current_page': page,
                'total_pages': paginator.num_pages,
                'total_records': paginator.count,
                'page_size': page_size,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous()
            }
        })

    except Exception as e:
        logger.error(f"Stock history API error: {str(e)}")
        return JsonResponse({
            'success': False,
            'status': 'error',
            'message': 'An error occurred while fetching stock history'
        }, status=500)

from core.throttling import CalculatorThrottle

@basic_security(throttle_class=CalculatorThrottle, require_post=True)
def investment_calculator(request):
    """API endpoint for investment calculator with enhanced security"""
    
    try:
        # Get parameters from request
        value = Decimal(request.POST.get('value', '0'))
        investment_type = request.POST.get('amountshare')  # 'shares' or 'amount'
        start_date = datetime.strptime(request.POST.get('startperiod'), '%Y-%m-%d').date()
        end_date = datetime.strptime(request.POST.get('endperiod'), '%Y-%m-%d').date()
        
        try:
            # First check if there are any prices in the date range
            prices_in_range = StockHistory.objects.filter(
                date__gte=min(start_date, end_date),
                date__lte=max(start_date, end_date)
            ).order_by('date')
            
            if prices_in_range.count() <= 1:
                # If only one or no price in range, get the closest previous price
                stock_price = StockHistory.objects.filter(
                    date__lte=max(start_date, end_date)
                ).order_by('-date').first()
                
                if not stock_price:
                    return JsonResponse({
                        'status': 'error',
                        'message': 'No stock data available for the selected dates'
                    })
                
                buy_price = sell_price = stock_price.close_price
            else:
                # Get buy price (first available price on or after start date)
                buy_stock = StockHistory.objects.filter(
                    date__gte=start_date
                ).order_by('date').first()
                
                if not buy_stock:
                    # If no price after start date, get the closest price before
                    buy_stock = StockHistory.objects.filter(
                        date__lte=start_date
                    ).order_by('-date').first()
                
                # Get sell price (last available price on or before end date)
                sell_stock = StockHistory.objects.filter(
                    date__lte=end_date
                ).order_by('-date').first()
                
                if not buy_stock or not sell_stock:
                    return JsonResponse({
                        'status': 'error',
                        'message': 'No stock data available for the selected dates'
                    })
                
                buy_price = buy_stock.close_price
                sell_price = sell_stock.close_price
            
            # Calculate investment results
            if investment_type == 'shares':
                # If user enters number of shares
                shares_amount = value
                final_value = shares_amount * sell_price
            else:  # amount
                # If user enters amount in EGP
                shares_amount = value / buy_price
                final_value = shares_amount * sell_price
            
            # Calculate percentage change (yield)
            change_percentage = ((sell_price - buy_price) / buy_price) * 100
            
            return JsonResponse({
                'status': 'success',
                'BuyPrice': float(buy_price),
                'SharesAmount': float(shares_amount),
                'SellPrice': float(sell_price),
                'CurrentValue': float(final_value),
                'ChangePercentage': float(change_percentage)
            })
            
        except StockHistory.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': 'No stock data available for the selected dates'
            })
            
    except (ValueError, TypeError, AttributeError) as e:
        logger.error(f"Investment calculator error: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': 'Invalid input data'
        })
    except Exception as e:
        logger.error(f"Unexpected error in investment calculator: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': 'An unexpected error occurred'
        })

def news_room_en(request):
    """English version of the news room page"""
    # Get common context first
    context = get_common_context('en')
    
    # Add hero sections for news page
    context['hero_sections'] = get_hero_sections(page='news', language='en')
    
    # Get recent press releases
    press_releases = PressRelease.objects.filter(
        language='en',
        is_active=True
    ).order_by('-date')[:10]
    
    # Debugging Press Releases
    press_releases_qs = PressRelease.objects.filter(
        language='en',
        is_active=True
    ).order_by('-publish_date')
    press_releases_list = list(press_releases_qs)
    print(f"--- DEBUG news_room_en: Press Releases Count: {len(press_releases_list)}")
    if len(press_releases_list) > 0:
        for pr in press_releases_list:
            file_url = pr.file.url if pr.file else 'N/A'
            print(f"--- DEBUG news_room_en: PR ID: {pr.id}, Title: {pr.title}, Date: {pr.publish_date}, URL: {file_url}, Active: {pr.is_active}, Lang: {pr.language}")
        
    # Debugging Newsroom Articles
    newsroom_articles_qs = NewsroomArticle.objects.all().order_by('-date')
    newsroom_articles_list = list(newsroom_articles_qs)
    print(f"--- DEBUG news_room_en: Newsroom Articles Count: {len(newsroom_articles_list)}")
    if len(newsroom_articles_list) > 0:
        for article in newsroom_articles_list:
            image_source = article.image.url if article.image else 'N/A'
            print(f"--- DEBUG news_room_en: Article ID: {article.id}, Title: {article.title}, Date: {article.date}, Image: {image_source}")

    context.update({
        'press_releases': press_releases_list,
        'ir_events': list(IRCalendarEvent.objects.filter(
            language='en',
            is_active=True,
            start_date__gte=timezone.now()
        ).order_by('start_date')[:5]),
        'newsroom_articles': newsroom_articles_list
    })
    # print(f"--- DEBUG news_room_en: Context Keys: {list(context.keys())}")
    # print(f"--- DEBUG news_room_en: Context newsroom_articles type: {type(context['newsroom_articles'])}")
    # print(f"--- DEBUG news_room_en: Context press_releases type: {type(context['press_releases'])}")
    return render(request, 'website/ir.gb-corporation.com/en/news-room.html', context)

def news_room_ar(request):
    """Arabic version of the news room page"""
    # Get common context first
    context = get_common_context('ar')
    
    # Add hero sections for news page
    context['hero_sections'] = get_hero_sections(page='news', language='ar')
    
    # Get recent press releases
    press_releases = PressRelease.objects.filter(
        language='ar',
        is_active=True
    ).order_by('-date')[:10]
    
    # Debugging Press Releases
    press_releases_qs_ar = PressRelease.objects.filter(
        language='ar',
        is_active=True
    ).order_by('-publish_date')
    press_releases_list_ar = list(press_releases_qs_ar)
    print(f"--- DEBUG news_room_ar: Press Releases Count: {len(press_releases_list_ar)}")
    if len(press_releases_list_ar) > 0:
        for pr in press_releases_list_ar:
            file_url = pr.file.url if pr.file else 'N/A'
            print(f"--- DEBUG news_room_ar: PR ID: {pr.id}, Title: {pr.title}, Date: {pr.publish_date}, URL: {file_url}, Active: {pr.is_active}, Lang: {pr.language}")
        
    # Debugging Newsroom Articles
    newsroom_articles_qs_ar = NewsroomArticle.objects.all().order_by('-date')
    newsroom_articles_list_ar = list(newsroom_articles_qs_ar)
    print(f"--- DEBUG news_room_ar: Newsroom Articles Count: {len(newsroom_articles_list_ar)}")
    if len(newsroom_articles_list_ar) > 0:
        for article in newsroom_articles_list_ar:
            image_source = article.image.url if article.image else 'N/A'
            print(f"--- DEBUG news_room_ar: Article ID: {article.id}, Title AR: {article.title_ar}, Date: {article.date}, Image: {image_source}")

    context.update({
        'press_releases': press_releases_list_ar,
        'ir_events': list(IRCalendarEvent.objects.filter(
            language='ar',
            is_active=True,
            start_date__gte=timezone.now()
        ).order_by('start_date')[:5]),
        'newsroom_articles': newsroom_articles_list_ar
    })
    # print(f"--- DEBUG news_room_ar: Context Keys: {list(context.keys())}")
    # print(f"--- DEBUG news_room_ar: Context newsroom_articles type: {type(context['newsroom_articles'])}")
    # print(f"--- DEBUG news_room_ar: Context press_releases type: {type(context['press_releases'])}")
    return render(request, 'website/ir.gb-corporation.com/ar/news-room.html', context)

from core.throttling import TimelineThrottle

@basic_security(throttle_class=TimelineThrottle, require_post=False)
def timeline_api(request, year=None, direction=None):
    """API endpoint for timeline navigation with enhanced security"""
    print("--- Timeline API Called ---")
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # Get parameters from URL or query params
        if direction is None:
            direction = request.GET.get('direction', 'next')
        
        if year is None:
            year = request.GET.get('year')
            
        language = request.GET.get('language', 'en')
        print(f"Params: Year={year}, Direction={direction}, Language={language}")
        
        # Get all timeline years
        timeline = HistoryTimeline.objects.filter(
            language=language
        ).order_by('-year')
        print(f"Timeline entries found for {language}: {timeline.count()}")
        
        if not timeline.exists():
            print("No timeline data found, returning error.")
            return JsonResponse({'success': False, 'message': 'No timeline data found'})
        
        # Get list of years
        years_list = list(timeline.values_list('year', flat=True))
        print(f"Available years: {years_list}")
        
        if not year:
            # If no current year provided, use the first year
            active_year = years_list[0]
            print(f"No year provided, defaulting to active_year: {active_year}")
        else:
            # Convert to int if it's a string
            try:
                current_active_year_from_request = int(year)
                active_year = current_active_year_from_request # Assume direct first
                print(f"Year provided: {current_active_year_from_request}, Direction: {direction}")
                
                # Navigation with arrows
                if direction == 'next':
                    # Find next year in the list
                    try:
                        current_index = years_list.index(current_active_year_from_request)
                        next_index = (current_index + 1) % len(years_list)
                        active_year = years_list[next_index]
                        print(f"Navigating NEXT from {current_active_year_from_request} to {active_year}")
                    except ValueError:
                        print(f"Error finding index for year {current_active_year_from_request}, defaulting to first year.")
                        active_year = years_list[0]
                elif direction == 'prev':
                    # Find previous year in the list
                    try:
                        current_index = years_list.index(current_active_year_from_request)
                        prev_index = (current_index - 1 + len(years_list)) % len(years_list) # Corrected modulo for prev
                        active_year = years_list[prev_index]
                        print(f"Navigating PREV from {current_active_year_from_request} to {active_year}")
                    except ValueError:
                        print(f"Error finding index for year {current_active_year_from_request}, defaulting to first year.")
                        active_year = years_list[0]
                elif direction == 'direct':
                     # Just use the year as provided
                     print(f"Direct navigation to year: {active_year}")
                     pass
                 
            except (ValueError, TypeError) as e:
                print(f"Error processing year/direction: {e}. Defaulting to first year.")
                active_year = years_list[0]
            
        # Get the active year entry and its events
        active_year_entry = timeline.filter(year=active_year).first()
        print(f"Selected active year: {active_year}")
        
        # Prepare the years data with active status
        years_data = []
        for y in years_list:
            years_data.append({
                'year': int(y),  # Convert to int to avoid comma formatting
                'active': y == active_year
            })
        print(f"Prepared years_data: {years_data}")
            
        # Get events for the active year
        events = []
        if active_year_entry and hasattr(active_year_entry, 'events'):
            print(f"Found entry for {active_year}. Events field type: {type(active_year_entry.events)}, Value: {active_year_entry.events}")
            if isinstance(active_year_entry.events, list):
                events = active_year_entry.events
            elif active_year_entry.events:
                # Attempt to parse if it's a string that looks like JSON
                if isinstance(active_year_entry.events, str) and active_year_entry.events.startswith('[') and active_year_entry.events.endswith(']'):
                    try:
                        import json
                        events = json.loads(active_year_entry.events)
                        print("Successfully parsed events string as JSON list.")
                    except json.JSONDecodeError:
                        print("Failed to parse events string as JSON, treating as single event.")
                        events = [str(active_year_entry.events)]
                else: # Treat as single event string
                    events = [str(active_year_entry.events)]
        else:
            print(f"No entry or events found for active year {active_year}")
            
        response_data = {
            'success': True,
            'years': years_data,
            'events': events,
            'active_year': int(active_year)
        }
        print(f"Returning JSON response: {response_data}")
        return JsonResponse(response_data)
        
    print("Invalid request (Not XMLHttpRequest)")
    return JsonResponse({'success': False, 'message': 'Invalid request'})

def initialize_values_data(request):
    """Initialize sample data for our-values page if no data exists"""
    # Check if data already exists
    company_overview_exists = CompanyOverview.objects.filter(language='en', is_active=True).exists()
    core_values_exist = CompanyCoreValue.objects.filter(language='en', is_active=True).exists()
    segments_exist = BusinessSegmentOverview.objects.filter(language='en', is_active=True).exists()
    timeline_exists = HistoryTimeline.objects.filter(language='en').exists()
    
    results = []
    
    # Create company overview if it doesn't exist
    if not company_overview_exists:
        company_overview = CompanyOverview.objects.create(
            title="GB Corp Overview",
            description="GB Corp S.A.E (GBCOCA on the Egyptian Exchange) is a leading automotive company in the Middle East and Africa and non-bank financial services provider in Egypt. Across six primary lines of business — Passenger Cars, Two, Three, and Four-Wheelers, Commercial Vehicles & Construction Equipment, and Trading Business — the company's main business activities include assembly, manufacturing, sales and distribution, financing and after-sales services.",
            vision="The world is constantly moving, transforming, and evolving. Change is inevitable and accelerating. At GB Corp, we aspire to make mobility in all its forms accessible for everyone.",
            mission="GB Corp provides integrated mobility solutions. Whether you are moving from point A to B, planning for a better opportunity, or aspiring for a better quality of life, we make the next step easier. With GB Corp, you will always be moving forward.",
            language="en",
            is_active=True
        )
        results.append(f"Created company overview: {company_overview.title}")
    else:
        results.append("Company overview already exists")
    
    # Create core values if they don't exist
    if not core_values_exist:
        core_values = [
            {"name": "We Are Ambitious Achievers", "order": 1},
            {"name": "We Act with Dignity and Respect", "order": 2},
            {"name": "We Are Resilient", "order": 3},
            {"name": "We Don't Compromise Quality", "order": 4},
            {"name": "We Operate as One Team", "order": 5}
        ]
        
        for value_data in core_values:
            value = CompanyCoreValue.objects.create(
                name=value_data["name"],
                description=f"Description for {value_data['name']}",
                language="en",
                order=value_data["order"],
                is_active=True
            )
            results.append(f"Created core value: {value.name}")
    else:
        results.append("Core values already exist")
    
    # Create business segments if they don't exist
    if not segments_exist:
        segments = [
            {"title": "Passenger Cars", "segment_type": "passenger_cars", "revenue_percentage": 75.0, "quarter": "Q4", "year": 2024, "order": 1},
            {"title": "Two, Three, and Four-Wheelers", "segment_type": "two_wheelers", "revenue_percentage": 3.0, "quarter": "Q4", "year": 2024, "order": 2},
            {"title": "Commercial Vehicles & Construction Equipment", "segment_type": "commercial_vehicles", "revenue_percentage": 6.3, "quarter": "Q4", "year": 2024, "order": 3},
            {"title": "Trading Business", "segment_type": "trading", "revenue_percentage": 4.1, "quarter": "Q4", "year": 2024, "order": 4},
            {"title": "GB Capital (Financing Businesses)", "segment_type": "financing", "revenue_percentage": 12.4, "quarter": "Q4", "year": 2024, "order": 5},
            {"title": "Startups", "segment_type": "startups", "revenue_percentage": 1.5, "quarter": "Q4", "year": 2024, "order": 6}
        ]
        
        for segment_data in segments:
            segment = BusinessSegmentOverview.objects.create(
                title=segment_data["title"],
                segment_type=segment_data["segment_type"],
                description=f"Description for {segment_data['title']}",
                revenue_percentage=segment_data["revenue_percentage"],
                quarter=segment_data["quarter"],
                year=segment_data["year"],

                language="en",
                order=segment_data["order"],
                is_active=True
            )
            
            # Create key points for each segment
            key_points = []
            if segment_data["segment_type"] == "passenger_cars":
                key_points = ["Passenger car sales and distribution", "After-sales service and parts", "Manufacturing and assembly"]
            elif segment_data["segment_type"] == "two_wheelers":
                key_points = ["Motorcycles and three-wheelers", "ATVs and utility vehicles", "Spare parts and accessories"]
            elif segment_data["segment_type"] == "commercial_vehicles":
                key_points = ["Commercial vehicle sales and distribution", "Construction equipment sales and rental", "After-sales service and parts"]
            elif segment_data["segment_type"] == "trading":
                key_points = ["Distribution of passenger car, tires, construction equipment and bus lines", "Distribution of three-wheeler or tuk-tuks lines", "After-sales service, Local Assembly, Component Manufacturing and Funding"]
            elif segment_data["segment_type"] == "financing":
                key_points = ["Leasing and financing solutions", "Consumer finance products", "Insurance and financial services"]
            elif segment_data["segment_type"] == "startups":
                key_points = ["Early-stage technology companies", "Innovation hubs and incubators", "Venture capital investments"]
            
            for i, point in enumerate(key_points):
                BusinessSegmentKeyPoint.objects.create(
                    segment=segment,
                    point=point,
                    order=i
                )
                
            results.append(f"Created segment: {segment.title} with {len(key_points)} key points")
    else:
        results.append("Business segments already exist")
    
    # Create timeline data if it doesn't exist
    if not timeline_exists:
        years = [2016, 2015, 2014, 2013, 2012]
        
        for i, year in enumerate(years):
            # Make 2014 the active year for the example
            events = []
            if year == 2014:
                events = [
                    "GB Auto's CLE 960 million capital increase was 99.86% covered",
                    "GB Auto supplies public transport authority in Alexandria with 150 buses",
                    "GB Auto joins forces with Cisco to drive information technology and ICT education in Egypt"
                ]
            elif year == 2015:
                events = [
                    "Expansion into African markets",
                    "Introduction of new vehicle models",
                    "Investments in manufacturing capabilities"
                ]
            elif year == 2016:
                events = [
                    "New executive leadership team announced",
                    "Partnership with global automotive brands",
                    "Corporate restructuring completed"
                ]
            elif year == 2013:
                events = [
                    "Market share growth in passenger vehicles segment",
                    "Opening of new service centers nationwide",
                    "Launch of financial services division"
                ]
            elif year == 2012:
                events = [
                    "Initial public offering completed",
                    "First manufacturing plant expansion",
                    "Creation of corporate governance structure"
                ]
                
            timeline = HistoryTimeline.objects.create(
                language="en",
                year=year,
                events=events
            )
            results.append(f"Created timeline entry for year {year} with {len(events)} events")
    else:
        results.append("Timeline data already exists")
    
    return HttpResponse("\n".join(results))

def populate_values(request):
    """
    Run the populate_values_data.py script to populate database with values data
    """
    try:
        # Execute the script
        result = subprocess.run(['python', 'populate_values_data.py'], 
                                capture_output=True, text=True, check=True)
        
        # Return success response with script output
        return JsonResponse({
            'status': 'success',
            'message': 'Values data populated successfully',
            'output': result.stdout
        })
    except subprocess.CalledProcessError as e:
        # Return error response if script execution fails
        return JsonResponse({
            'status': 'error',
            'message': 'Failed to populate values data',
            'error': str(e),
            'output': e.stdout,
            'stderr': e.stderr
        }, status=500)

def fix_timeline_data(request):
    """Temporary view to fix timeline data"""
    from django.http import HttpResponse
    from website.models import HistoryTimeline
    import traceback
    
    response = []
    response.append("<h1>Timeline Data Fix</h1>")
    
    try:
        # Print existing timeline years
        en_years = [t.year for t in HistoryTimeline.objects.filter(language='en').order_by('-year')]
        ar_years = [t.year for t in HistoryTimeline.objects.filter(language='ar').order_by('-year')]
        
        response.append(f"<p>Current English years: {en_years}</p>")
        response.append(f"<p>Current Arabic years: {ar_years}</p>")
        
        # Find missing years
        missing_years = [year for year in en_years if year not in ar_years]
        response.append(f"<p>Missing years in Arabic: {missing_years}</p>")
        
        # Define the missing Arabic timeline entries
        missing_ar_timeline = [
            {
                "year": 2021,
                "events": [
                    "شركة جي بي أوتو تفتخر بتوريد حافلات كأس الأمم الأفريقية 2021-2022",
                    "إطلاق مبادرة غالية بواسطة غبور",
                    "شركة جي بي أوتو تتشرف بتلقي جائزة أموال الغد 2022",
                    "شركة درايف تختتم إصدار السندات الأول بقيمة 700 مليون جنيه مصري"
                ]
            },
            {
                "year": 2020,
                "events": [
                    "إعلان شركة MNT-Halan عن استثمار بقيمة 120 مليون دولار أمريكي من قبل مستثمرين عالميين وإقليميين",
                    "إعلان شركة جي بي أوتو عن تنفيذ إحدى شركاتها التابعة اتفاقية مع صناديق استثمار أجنبية",
                    "إطلاق سيارة هافال 2022 بتكنولوجيا مبتكرة بشكل غير مسبوق",
                    "الاستحواذ الرسمي على وكالة شانجان الحصرية وتقديم 4 طرازات جديدة"
                ]
            },
            {
                "year": 2013,
                "events": [
                    "نمو حصة السوق في قطاع سيارات الركاب",
                    "افتتاح مراكز خدمة جديدة في جميع أنحاء البلاد",
                    "إطلاق قسم الخدمات المالية"
                ]
            },
            {
                "year": 2012,
                "events": [
                    "إتمام الطرح العام الأولي",
                    "توسيع أول مصنع للإنتاج",
                    "إنشاء هيكل حوكمة الشركات"
                ]
            }
        ]
        
        ar_timeline_entries = []
        
        # Create Arabic timeline entries
        for entry in missing_ar_timeline:
            year = entry["year"]
            response.append(f"<p>Processing year {year}...</p>")
            
            # Check if entry already exists
            existing = HistoryTimeline.objects.filter(
                year=year,
                language='ar'
            ).first()
            
            if existing:
                response.append(f"<p>Entry for year {year} already exists in Arabic. Skipping.</p>")
                continue
            
            response.append(f"<p>Creating new entry for year {year} with {len(entry['events'])} events.</p>")
            
            timeline = HistoryTimeline(
                year=year,
                language='ar',
                events=entry["events"]
            )
            timeline.save()
            response.append(f"<p>Successfully saved entry for year {year}.</p>")
            ar_timeline_entries.append(timeline)
        
        response.append(f"<p>Added {len(ar_timeline_entries)} missing Arabic timeline entries</p>")
        
        # Print final timeline years after additions
        en_years = [t.year for t in HistoryTimeline.objects.filter(language='en').order_by('-year')]
        ar_years = [t.year for t in HistoryTimeline.objects.filter(language='ar').order_by('-year')]
        
        response.append(f"<p>Final English years: {en_years}</p>")
        response.append(f"<p>Final Arabic years: {ar_years}</p>")
        
    except Exception as e:
        response.append(f"<p>Error: {str(e)}</p>")
        response.append(f"<pre>{traceback.format_exc()}</pre>")
    
    return HttpResponse("\n".join(response))

def earnings_newsletter_redirect(request, period=None, year=None):
    """
    Redirects to the earnings newsletter URL stored in the database.
    Constructs the display name and retrieves URL from EarningsResult model.
    """
    from django.shortcuts import redirect
    from django.http import Http404
    
    # If no parameters provided, try to get the latest result
    if not period or not year:
        try:
            latest = EarningsResult.objects.filter(is_active=True).order_by('-year', '-period').first()
            if latest and latest.earnings_newsletter_url:
                return redirect(latest.earnings_newsletter_url)
            else:
                raise Http404("No active earnings results found in database")
        except Exception as e:
            raise Http404("Error retrieving latest earnings result from database")
    
    # Handle quarter period formatting (ensure it starts with Q)
    if period and not period.startswith('Q'):
        if period in ['1', '2', '3', '4']:
            period = f"Q{period}"
        else:
            # Handle special cases like 3Q -> Q3
            if period.endswith('Q'):
                quarter_num = period[0]
                period = f"Q{quarter_num}"
            elif len(period) == 3 and period[1] == 'Q':
                # Format: 3Q24 -> Q3
                quarter_num = period[0]
                period = f"Q{quarter_num}"
    
    # Ensure year is in correct format
    if year and len(year) == 2:
        # Convert 24 to 2024 for database lookup
        year_full = f"20{year}"
    else:
        year_full = year
    
    try:
        # Try to find the specific earnings result in the database
        result = EarningsResult.objects.filter(
            period=period,
            year=year_full,
            is_active=True
        ).first()
        
        if result and result.earnings_newsletter_url:
            return redirect(result.earnings_newsletter_url)
        else:
            # If specific result not found, try to get the latest available
            latest = EarningsResult.objects.filter(is_active=True).order_by('-year', '-period').first()
            if latest and latest.earnings_newsletter_url:
                return redirect(latest.earnings_newsletter_url)
            else:
                raise Http404("No earnings newsletter URL found in database")
                
    except Exception as e:
        raise Http404("Error retrieving earnings result from database")

def get_earnings_newsletter(request, display_name=None):
    """
    Gets the earnings newsletter URL directly from the database and redirects.
    
    Example: If display_name is 'GBCorp3Q24', this will extract period='Q3' and year='24',
    find the matching record in the database, and redirect to its URL.
    """
    from django.shortcuts import redirect
    from django.http import Http404
    import re
    
    if not display_name:
        # Get the latest earnings result
        try:
            latest = EarningsResult.objects.order_by('-year', '-period').first()
            if latest:
                return redirect(latest.earnings_newsletter_url)
        except Exception as e:
            print(f"Error retrieving latest earnings result: {e}")
    else:
        # Parse the display name to extract period and year
        # Format is expected to be GBCorpQ324 or similar
        pattern = r'GBCorp([Q][1-4])(\d{2})'
        match = re.match(pattern, display_name)
        
        if match:
            period = match.group(1)
            year = match.group(2)
            
            try:
                # Find the matching record in the database
                result = EarningsResult.objects.filter(period=period, year=year).first()
                if result:
                    # Return direct redirect to the stored URL
                    return redirect(result.earnings_newsletter_url)
            except Exception as e:
                print(f"Error retrieving earnings result for {period} {year}: {e}")
    
    # If we reach here, either the display_name wasn't valid or we couldn't find a matching record
    # Fall back to a default URL or raise a 404
    try:
        # Try to get the latest result as a fallback
        latest = EarningsResult.objects.order_by('-year', '-period').first()
        if latest:
            return redirect(latest.earnings_newsletter_url)
    except Exception:
        pass
    
    # If all else fails, raise a 404 instead of using hardcoded URL
    raise Http404("No earnings newsletter URL found in database")

def earnings_result_view(request):
    """
    Display the details of earnings results and redirect to the selected URL.
    """
    from django.shortcuts import render, redirect
    
    # Get all earnings results
    results = EarningsResult.objects.all()
    
    # Check if there's a redirect action
    url_type = request.GET.get('type')
    result_id = request.GET.get('id')
    
    if url_type and result_id:
        try:
            result = EarningsResult.objects.get(id=result_id)
            if url_type == 'newsletter':
                return redirect(result.earnings_newsletter_url)
            elif url_type == 'release':
                return redirect(result.earnings_release_url)
        except EarningsResult.DoesNotExist:
            pass  # If result doesn't exist, just display the page
    
    context = {
        'results': results,
        'page_title': 'Earnings Results'
    }
    
    return render(request, 'website/earnings_results.html', context)

def earnings_newsletter_direct(request, result_id):
    """
    Directly redirects to the earnings newsletter URL from the database without any formatting issues.
    This avoids problems with commas in year values.
    """
    from django.shortcuts import redirect, get_object_or_404
    
    result = get_object_or_404(EarningsResult, id=result_id)
    return redirect(result.earnings_newsletter_url)

def latest_newsletter_redirect(request):
    """
    Simply gets the latest earnings newsletter URL from the database and redirects to it.
    No parameters needed - just finds the most recent record.
    """
    from django.shortcuts import redirect
    from django.http import HttpResponse, Http404
    
    # Get the most recent earnings result
    latest = EarningsResult.objects.order_by('-year', '-period').first()
    
    if latest and latest.earnings_newsletter_url:
        # Redirect directly to the URL stored in the database
        return redirect(latest.earnings_newsletter_url)
    else:
        # Return a 404 if the record doesn't exist or URL is empty
        raise Http404("Newsletter URL not found in database")

def check_earnings_urls(request):
    """
    Admin view to check and manage earnings result URLs.
    Shows the actual URLs stored in the database so they can be verified.
    """
    from django.http import HttpResponse
    from django.contrib.auth.decorators import user_passes_test
    from django.urls import reverse
    
    # Get all earnings results
    results = EarningsResult.objects.all().order_by('-year', '-period')
    
    html = "<html><body><h1>Earnings URLs in Database</h1>"
    html += "<table border='1'>"
    html += "<tr><th>ID</th><th>Period</th><th>Year</th><th>Display Name</th><th>Newsletter URL</th><th>Release URL</th><th>Actions</th></tr>"
    
    for result in results:
        html += f"<tr>"
        html += f"<td>{result.id}</td>"
        html += f"<td>{result.period}</td>"
        html += f"<td>{result.year}</td>"
        html += f"<td>GBCorp{result.period}{result.year}</td>"
        html += f"<td><a href='{result.earnings_newsletter_url}' target='_blank'>{result.earnings_newsletter_url}</a></td>"
        html += f"<td><a href='{result.earnings_release_url}' target='_blank'>{result.earnings_release_url}</a></td>"
        html += f"<td><a href='{reverse('website:update_earnings_url', args=[result.id])}'>Edit</a></td>"
        html += f"</tr>"
    
    html += "</table>"
    html += "<p>Note: Make sure URLs are correct. The 'Latest Earnings Results' links on the homepage will redirect to these URLs.</p>"
    html += "</body></html>"
    
    return HttpResponse(html)

def update_earnings_url(request, result_id):
    """
    Updates the URL for a specific earnings result.
    """
    from django.http import HttpResponse, HttpResponseRedirect
    from django.contrib.auth.decorators import user_passes_test
    from django.urls import reverse
    
    if request.method == 'POST':
        newsletter_url = request.POST.get('newsletter_url')
        release_url = request.POST.get('release_url')
        
        try:
            result = EarningsResult.objects.get(id=result_id)
            
            # Update URLs if provided
            if newsletter_url:
                result.earnings_newsletter_url = newsletter_url
            if release_url:
                result.earnings_release_url = release_url
                
            result.save()
            return HttpResponseRedirect(reverse('website:check_earnings_urls'))
            
        except EarningsResult.DoesNotExist:
            return HttpResponse("Error: Earnings result not found", status=404)
    
    # If not a POST request, show the form
    try:
        result = EarningsResult.objects.get(id=result_id)
        
        html = "<html><body>"
        html += f"<h1>Update URLs for GBCorp{result.period}{result.year}</h1>"
        html += "<form method='post' action=''>"
        html += "{% csrf_token %}"
        html += f"<p>Newsletter URL: <input type='text' name='newsletter_url' value='{result.earnings_newsletter_url}' size='100'></p>"
        html += f"<p>Release URL: <input type='text' name='release_url' value='{result.earnings_release_url}' size='100'></p>"
        html += "<input type='submit' value='Update'>"
        html += "</form>"
        html += f"<p><a href='{reverse('website:check_earnings_urls')}'>Back to list</a></p>"
        html += "</body></html>"
        
        return HttpResponse(html)
        
    except EarningsResult.DoesNotExist:
        return HttpResponse("Error: Earnings result not found", status=404)

def newsletter_redirect(request):
    """
    Redirects to the earnings newsletter URL from the latest result in the database.
    """
    from django.shortcuts import redirect
    from django.http import Http404
    
    # Get the single most recent earnings result
    latest = EarningsResult.objects.all().order_by('-year', '-period').first()
    
    if latest and latest.earnings_newsletter_url:
        return redirect(latest.earnings_newsletter_url)
    else:
        raise Http404("Newsletter URL not found in database")

def release_redirect(request):
    """
    Redirects to the earnings release URL from the latest result in the database.
    """
    from django.shortcuts import redirect
    from django.http import Http404
    
    # Get the single most recent earnings result
    latest = EarningsResult.objects.all().order_by('-year', '-period').first()
    
    if latest and latest.earnings_release_url:
        return redirect(latest.earnings_release_url)
    else:
        raise Http404("Release URL not found in database")

# Add a new view to directly check context data
def check_context(request):
    """Debug view to check what's in the database right now"""
    from django.http import JsonResponse
    
    data = {
        'investor_presentations': [
            {
                'id': p.id,
                'title': p.title,
                'is_active': p.is_active,
                'language': p.language,
                'year': p.year,
                'period': p.period,
            } 
            for p in InvestorPresentation.objects.all()
        ],
        'annual_reports': [
            {
                'id': r.id,
                'title': r.title,
                'is_active': r.is_active,
                'language': r.language,
                'year': r.year,
            }
            for r in AnnualReport.objects.all()
        ],
        'esg_reports': [
            {
                'id': r.id,
                'title': r.title,
                'is_active': r.is_active,
                'language': r.language,
                'year': r.year,
                'report_type': r.report_type,
            }
            for r in ESGReport.objects.all()
        ],
    }
    
    return JsonResponse(data)

def fix_earnings_data(request):
    """
    Direct fix to ensure we have at least one active Year and EarningsResult record
    so the homepages show data in the tables. ALSO, prints current Year status.
    """
    from django.http import HttpResponse
    
    output = ["<h1>IR Website Data Fix & Year Status</h1>"]
    
    # --- Show current Year status --- 
    output.append("<h2>Current Year Records Status:</h2>")
    all_years = Year.objects.all().order_by('-year')
    if all_years.exists():
        output.append("<ul>")
        for yr in all_years:
            output.append(f"<li>Year: {yr.year}, is_active: {yr.is_active}</li>")
        output.append("</ul>")
    else:
        output.append("<p>No Year records found in the database.</p>")
    output.append("<hr>")
    # --- End Year status --- 
    
    output.append("<h2>Attempting Fixes:</h2>")
    # First, check if there are any Year records
    years_count = Year.objects.count()
    active_years_count = Year.objects.filter(is_active=True).count()
    
    output.append(f"<p>Found {years_count} Year records, with {active_years_count} active.</p>")
    
    # Create a Year for the current year if none exists
    current_year = datetime.now().year
    year_obj, year_created = Year.objects.get_or_create(
        year=current_year,
        defaults={'is_active': True}
    )
    
    if year_created:
        output.append(f"<p>Created new Year record for {current_year} with is_active=True</p>")
    else:
        # Ensure the year is active
        if not year_obj.is_active:
            year_obj.is_active = True
            year_obj.save()
            output.append(f"<p>Updated existing Year record for {current_year} to is_active=True</p>")
        else:
            output.append(f"<p>Year record for {current_year} already exists and is active</p>")
    
    # Now check for EarningsResult records
    earnings_count = EarningsResult.objects.count()
    # Find the most recent earnings result year
    latest_earnings_result = EarningsResult.objects.order_by('-year').first()
    target_year = latest_earnings_result.year if latest_earnings_result else current_year
    
    earnings_for_target_year = EarningsResult.objects.filter(year=target_year).count()
    
    output.append(f"<p>Found {earnings_count} EarningsResult records. Checking for year {target_year}. Found {earnings_for_target_year} results for {target_year}.</p>")
    
    # Create an EarningsResult for the target year if none exists
    if earnings_for_target_year == 0:
        # Ensure the corresponding Year object exists and is active
        target_year_obj, target_year_created = Year.objects.get_or_create(
            year=target_year,
            defaults={'is_active': True}
        )
        if not target_year_obj.is_active:
            target_year_obj.is_active = True
            target_year_obj.save()
            output.append(f"<p>Activated Year record for {target_year}</p>")
            
        # Create a new EarningsResult with today's publish_date
        result = EarningsResult.objects.create(
            year=target_year,
            period="Q3", # Use a default period
            publish_date=datetime.now().date(),  # Add today's date as publish_date
            earnings_newsletter_url="",  # Should be set via admin
            earnings_release_url=""  # Should be set via admin
        )
        output.append(f"<p>Created new EarningsResult record for {target_year} Q3 (URLs should be set via admin)</p>")
    else:
        output.append(f"<p>EarningsResult records for {target_year} already exist</p>")
    
    # Add a direct link to go back to the homepage
    output.append("<hr><p><a href='/'>Return to homepage</a></p>")
    
    return HttpResponse("".join(output))

# Add a direct function to create test presentations
def create_test_data(request):
    """Create test data directly from browser request"""
    from django.http import JsonResponse
    from datetime import datetime, timedelta
    from django.core.files.base import ContentFile
    
    results = {
        'created': [],
        'errors': []
    }
    
    try:
        # Create English presentations
        today = datetime.now().date()
        
        # InvestorPresentation
        try:
            presentation = InvestorPresentation(
                title='Q1 2024 Investor Presentation',
                period='Q1',
                year=2024,
                presentation_date=today - timedelta(days=30),
                description='Q1 2024 financial results presentation',
                is_active=True,
                language='en'
            )
            
            # Save files separately
            presentation.document_file.save('q1_2024_presentation.pdf', 
                                        ContentFile(b'Test presentation content'), 
                                        save=False)
            presentation.cover_image.save('q1_2024_cover.jpg', 
                                      ContentFile(b'Test image content'), 
                                      save=False)
            presentation.save()
            results['created'].append(f"Created presentation: id={presentation.id}")
        except Exception as e:
            results['errors'].append(f"Error creating presentation: {str(e)}")
        
        # AnnualReport
        try:
            annual_report = AnnualReport(
                title='Annual Report 2023',
                year=2023,
                description='Annual report for fiscal year 2023',
                language='en',
                is_active=True
            )
            annual_report.save()
            results['created'].append(f"Created annual report: id={annual_report.id}")
        except Exception as e:
            results['errors'].append(f"Error creating annual report: {str(e)}")
        
        # ESGReport
        try:
            esg_report = ESGReport(
                title='Sustainability Report 2023',
                year=2023,
                description='Our sustainability initiatives and performance',
                report_type='sustainability',
                language='en',
                is_active=True,
                publish_date=today - timedelta(days=60)
            )
            esg_report.save()
            results['created'].append(f"Created ESG report: id={esg_report.id}")
        except Exception as e:
            results['errors'].append(f"Error creating ESG report: {str(e)}")
            
    except Exception as e:
        results['errors'].append(f"General error: {str(e)}")
    
    # Add current counts
    results['counts'] = {
        'presentations': InvestorPresentation.objects.count(),
        'annual_reports': AnnualReport.objects.count(),
        'esg_reports': ESGReport.objects.count()
    }
    
    return JsonResponse(results)

def get_settings(language='en'):
    """Get website settings"""
    from website.models import WebsiteSetting
    return {setting.key: setting.value for setting in WebsiteSetting.objects.filter(is_public=True)}

def get_hero_sections(page=None, language='en'):
    """Get hero sections for a specific page or all pages"""
    from website.models import HeroSection
    
    # Force English language for share-information and corporate-information pages
    if page == 'ir':
        language = 'en'
    
    query = {'language': language, 'is_active': True}
    if page:
        query['page'] = page
    
    hero_sections = HeroSection.objects.filter(**query).order_by('page', 'order')
    
    # Group by page if no specific page was requested
    if not page:
        result = {}
        for section in hero_sections:
            if section.page not in result:
                result[section.page] = []
            result[section.page].append(section)
        return result
    
    return hero_sections

def get_home_gallery(language='en'):
    """Get home gallery items"""
    from website.models import HomeGallery
    
    return HomeGallery.objects.filter(
        language=language,
        is_active=True
    ).order_by('order')

def get_language():
    """Get the current language (simplified version)"""
    # In a real implementation, this would check the request, cookies, etc.
    # For now, just return 'en' as the default
    return 'en'

def create_financial_highlights_test_data(request):
    """Create test financial highlights data for testing"""
    from decimal import Decimal
    
    # Create English financial highlights
    financial_highlight_en, created = FinancialHighlight.objects.get_or_create(
        period='9M',
        year=2024,
        language='en',
        defaults={
            'title': '9M24 Financial Highlights',
            'metrics': {
                'revenues': 35.4,
                'gross_profit': 7.2,
                'net_income': 1.8
            },
            'currency': 'EGP',
            'is_active': True
        }
    )
    
    # Create Arabic financial highlights
    financial_highlight_ar, created = FinancialHighlight.objects.get_or_create(
        period='9M',
        year=2024,
        language='ar',
        defaults={
            'title': 'المؤشرات المالية للأشهر التسعة الأولى من 2024',
            'metrics': {
                'revenues': 35.4,
                'gross_profit': 7.2,
                'net_income': 1.8
            },
            'currency': 'EGP',
            'is_active': True
        }
    )
    
    return JsonResponse({
        'status': 'success',
        'message': 'Financial highlights test data created',
        'en_created': created,
        'ar_created': created
    })

@require_http_methods(["POST"])
def newsletter_subscribe(request):
    """
    Handle newsletter subscription requests with enhanced security
    """
    language = 'en'  # Default language in case of early errors
    try:
        # Import security modules
        from core.email_security import email_security_validator
        from core.subscription_security import subscription_abuse_detector
        from core.throttling import EnhancedNewsletterThrottle
        from core.security_monitoring import security_monitor, SecurityEventType

        # Get client IP and user agent
        ip_address = get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')

        # Apply rate limiting
        throttle = EnhancedNewsletterThrottle()
        if not throttle.allow_request(request, None):
            return JsonResponse({
                'success': False,
                'message': 'Too many requests. Please try again later.' if request.GET.get('language', 'en') == 'en' else 'طلبات كثيرة جداً. يرجى المحاولة مرة أخرى لاحقاً.'
            }, status=429)

        data = json.loads(request.body)
        email = data.get('email', '').strip().lower()
        name = data.get('name', '').strip()
        language = data.get('language', 'en')

        # Enhanced email security validation
        email_validation = email_security_validator.validate_email_security(email, ip_address)
        if not email_validation['valid']:
            # Log security event
            security_monitor.log_security_event(
                SecurityEventType.EMAIL_SECURITY_BLOCK,
                email,
                ip_address,
                {
                    'user_agent': user_agent,
                    'validation_details': email_validation,
                    'request_data': data
                },
                'medium'
            )

            return JsonResponse({
                'success': False,
                'message': email_validation['reason'] if language == 'en' else 'عنوان البريد الإلكتروني غير صالح أو محظور.',
                'security_block': True
            }, status=400)

        # Subscription abuse detection
        abuse_check = subscription_abuse_detector.check_subscription_abuse(email, ip_address, user_agent)
        if abuse_check['block_subscription']:
            # Log security event
            security_monitor.log_security_event(
                SecurityEventType.ABUSE_DETECTED,
                email,
                ip_address,
                {
                    'user_agent': user_agent,
                    'abuse_details': abuse_check,
                    'request_data': data
                },
                'high'
            )

            return JsonResponse({
                'success': False,
                'message': 'Subscription request blocked due to suspicious activity.' if language == 'en' else 'تم حظر طلب الاشتراك بسبب نشاط مشبوه.',
                'abuse_detected': True,
                'require_captcha': abuse_check.get('require_captcha', False)
            }, status=403)

        # Honeypot protection
        from core.honeypot_protection import honeypot_protection
        honeypot_check = honeypot_protection.validate_honeypot_submission(data, ip_address)
        if honeypot_check['block_submission']:
            # Log security event
            security_monitor.log_security_event(
                SecurityEventType.BOT_DETECTED,
                email,
                ip_address,
                {
                    'user_agent': user_agent,
                    'honeypot_details': honeypot_check,
                    'request_data': data
                },
                'high'
            )

            return JsonResponse({
                'success': False,
                'message': 'Bot detected. Please try again.' if language == 'en' else 'تم اكتشاف روبوت. يرجى المحاولة مرة أخرى.',
                'bot_detected': True
            }, status=403)
        
        # Get client IP
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip_address = x_forwarded_for.split(',')[0]
        else:
            ip_address = request.META.get('REMOTE_ADDR')
        
        # Check if already subscribed
        existing_subscription = NewsletterSubscription.objects.filter(email=email).first()
        
        if existing_subscription:
            if existing_subscription.status == 'subscribed':
                return JsonResponse({
                    'success': False,
                    'message': 'This email is already subscribed to our newsletter.' if language == 'en' else 'هذا البريد الإلكتروني مشترك بالفعل في نشرتنا الإخبارية.',
                    'already_subscribed': True
                })
            elif existing_subscription.status == 'unsubscribed':
                # Resubscribe
                existing_subscription.status = 'subscribed'
                existing_subscription.name = name or existing_subscription.name
                existing_subscription.language_preference = language
                existing_subscription.subscribe_date = timezone.now()
                existing_subscription.unsubscribe_date = None
                existing_subscription.save()
                
                return JsonResponse({
                    'success': True,
                    'message': 'Welcome back! You have been resubscribed to our newsletter.' if language == 'en' else 'مرحباً بعودتك! تم إعادة اشتراكك في نشرتنا الإخبارية.'
                })
            else:
                # Update pending/bounced subscription
                existing_subscription.name = name or existing_subscription.name
                existing_subscription.language_preference = language
                existing_subscription.status = 'subscribed'
                existing_subscription.subscribe_date = timezone.now()
                existing_subscription.save()
                
                return JsonResponse({
                    'success': True,
                    'message': 'Thank you for subscribing to our newsletter!' if language == 'en' else 'شكراً لك على الاشتراك في نشرتنا الإخبارية!'
                })
        else:
            # Create new subscription
            subscription = NewsletterSubscription.objects.create(
                email=email,
                name=name,
                status='subscribed',
                language_preference=language,
                source='website',
                ip_address=ip_address,
                subscribe_date=timezone.now()
            )

            # Log successful new subscription
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"New subscription created for {email} from IP {ip_address}")

            # Log successful subscription event
            security_monitor.log_security_event(
                'successful_subscription',
                email,
                ip_address,
                {
                    'user_agent': user_agent,
                    'subscription_type': 'new',
                    'language': language,
                    'source': 'website'
                },
                'low'
            )

            return JsonResponse({
                'success': True,
                'message': 'Thank you for subscribing to our newsletter!' if language == 'en' else 'شكراً لك على الاشتراك في نشرتنا الإخبارية!'
            })
            
    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': 'Invalid request format.' if language == 'en' else 'تنسيق طلب غير صالح.'
        }, status=400)
    except Exception as e:
        # Log the error for debugging
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Newsletter subscription error: {str(e)}")

        return JsonResponse({
            'success': False,
            'message': 'An error occurred. Please try again later.' if language == 'en' else 'حدث خطأ. يرجى المحاولة مرة أخرى لاحقاً.'
        }, status=500)

@require_http_methods(["GET"])
def security_dashboard(request):
    """
    Security dashboard for administrators
    """
    # Check if user is admin/staff
    if not request.user.is_staff:
        return JsonResponse({'error': 'Access denied'}, status=403)

    from core.security_monitoring import security_monitor

    # Get dashboard data
    hours = int(request.GET.get('hours', 24))
    dashboard_data = security_monitor.get_security_dashboard_data(hours)

    return JsonResponse(dashboard_data)

@require_http_methods(["GET"])
def ip_security_status(request, ip_address):
    """
    Get security status for a specific IP address
    """
    # Check if user is admin/staff
    if not request.user.is_staff:
        return JsonResponse({'error': 'Access denied'}, status=403)

    from core.security_monitoring import security_monitor

    status_data = security_monitor.get_ip_security_status(ip_address)
    return JsonResponse(status_data)

@require_http_methods(["POST"])
def block_ip(request):
    """
    Manually block an IP address
    """
    # Check if user is admin/staff
    if not request.user.is_staff:
        return JsonResponse({'error': 'Access denied'}, status=403)

    try:
        data = json.loads(request.body)
        ip_address = data.get('ip_address')
        duration = int(data.get('duration', 3600))  # Default 1 hour
        reason = data.get('reason', 'Manual admin block')

        if not ip_address:
            return JsonResponse({'error': 'IP address required'}, status=400)

        from core.security_monitoring import security_monitor
        security_monitor.block_ip_address(ip_address, duration, reason)

        return JsonResponse({'success': True, 'message': f'IP {ip_address} blocked for {duration} seconds'})

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@require_http_methods(["POST"])
def unblock_ip(request):
    """
    Manually unblock an IP address
    """
    # Check if user is admin/staff
    if not request.user.is_staff:
        return JsonResponse({'error': 'Access denied'}, status=403)

    try:
        data = json.loads(request.body)
        ip_address = data.get('ip_address')
        reason = data.get('reason', 'Manual admin unblock')

        if not ip_address:
            return JsonResponse({'error': 'IP address required'}, status=400)

        from core.security_monitoring import security_monitor
        security_monitor.unblock_ip_address(ip_address, reason)

        return JsonResponse({'success': True, 'message': f'IP {ip_address} unblocked'})

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@require_http_methods(["GET"])
def newsletter_unsubscribe(request, token):
    """
    Handle newsletter unsubscription via token
    """
    try:
        subscription = NewsletterSubscription.objects.filter(
            unsubscribe_token=token
        ).first()
        
        if not subscription:
            return render(request, 'website/newsletter_unsubscribe.html', {
                'success': False,
                'message': 'Invalid unsubscribe link.'
            })
        
        if subscription.status == 'unsubscribed':
            return render(request, 'website/newsletter_unsubscribe.html', {
                'success': True,
                'message': 'You are already unsubscribed from our newsletter.',
                'already_unsubscribed': True
            })
        
        # Unsubscribe the user
        subscription.status = 'unsubscribed'
        subscription.unsubscribe_date = timezone.now()
        subscription.save()
        
        return render(request, 'website/newsletter_unsubscribe.html', {
            'success': True,
            'message': 'You have been successfully unsubscribed from our newsletter.',
            'email': subscription.email
        })
        
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Newsletter unsubscription error: {str(e)}")
        
        return render(request, 'website/newsletter_unsubscribe.html', {
            'success': False,
            'message': 'An error occurred. Please try again later.'
        })

from core.throttling import NewsletterCheckThrottle

@require_http_methods(["POST"])
@csrf_exempt
@enhanced_security(throttle_class=NewsletterCheckThrottle, require_post=True, validate_email=False)
def newsletter_check_subscription(request):
    """
    Check if an email is already subscribed with anti-enumeration protection
    """
    try:
        data = json.loads(request.body)
        email = data.get('email', '').strip().lower()

        if not email:
            return JsonResponse({
                'success': False,
                'message': 'Email is required.'
            }, status=400)

        # Basic email format validation to prevent enumeration with invalid emails
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            return JsonResponse({
                'success': False,
                'message': 'Invalid email format.'
            }, status=400)

        # Add delay to prevent timing attacks
        import time
        import random
        time.sleep(random.uniform(0.1, 0.3))  # Random delay 100-300ms

        subscription = NewsletterSubscription.objects.filter(email=email).first()

        # Log the check attempt for monitoring
        from core.security_monitoring import security_monitor, SecurityEventType
        from api.tools import get_client_ip

        security_monitor.log_security_event(
            SecurityEventType.SUBSCRIPTION_SUCCESS,  # Reusing for check events
            email,
            get_client_ip(request),
            {
                'endpoint': 'newsletter_check',
                'found': bool(subscription),
                'status': subscription.status if subscription else 'not_found'
            },
            'low'
        )

        if subscription:
            return JsonResponse({
                'success': True,
                'subscribed': subscription.status == 'subscribed',
                'status': subscription.status,
                'subscribe_date': subscription.subscribe_date.isoformat() if subscription.subscribe_date else None
            })
        else:
            return JsonResponse({
                'success': True,
                'subscribed': False,
                'status': 'not_found'
            })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': 'Invalid request format.'
        }, status=400)
    except Exception as e:
        logger.error(f"Newsletter check error: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': 'An error occurred.'
        }, status=500)