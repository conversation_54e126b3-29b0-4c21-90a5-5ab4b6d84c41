{% extends "website/ir.gb-corporation.com/en/base.html" %}
{% load static %}
{% load custom_filters %}

{% block extra_css %}
<!-- Include both headers -->

{% include "website/ir.gb-corporation.com/includes/header.html" %}

<!-- Styles to handle multiple headers -->
<style>
/* Style adjustments for multiple headers */
.main-header:nth-of-type(1) {
    /* First header (Arabic) - primary display */
    z-index: 1001;
}

.main-header:nth-of-type(2) {
    /* Second header (English) - secondary */
    z-index: 1000;
    display: none; /* Hide by default, can be toggled */
}

.stock-price-banner:nth-of-type(1) {
    /* First stock banner (Arabic) - primary display */
    z-index: 1002;
}

.stock-price-banner:nth-of-type(2) {
    /* Second stock banner (English) - secondary */
    z-index: 999;
    display: none; /* Hide by default */
}

/* Toggle button for switching headers */
.header-toggle {
    position: fixed;
    top: 50px;
    right: 10px;
    z-index: 2000;
    background: #003C7F;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
}

.header-toggle:hover {
    background: #004B87;
}
</style>

<!-- Add header toggle button -->


<script>
function toggleHeaders() {
    const arabicHeaders = document.querySelectorAll('.main-header:nth-of-type(1), .stock-price-banner:nth-of-type(1)');
    const englishHeaders = document.querySelectorAll('.main-header:nth-of-type(2), .stock-price-banner:nth-of-type(2)');
    
    arabicHeaders.forEach(header => {
        if (header.style.display === 'none') {
            header.style.display = 'flex';
        } else {
            header.style.display = 'none';
        }
    });
    
    englishHeaders.forEach(header => {
        if (header.style.display === 'none') {
            header.style.display = 'flex';
        } else {
            header.style.display = 'none';
        }
    });
}
</script>

<!-- Add Highcharts libraries -->
<script src="/static/external/code.highcharts.com/stock/highstock.js"></script>
<script src="/static/external/code.highcharts.com/stock/modules/data.js"></script>
<script src="/static/external/code.highcharts.com/stock/modules/exporting.js"></script>
<script src="/static/external/code.highcharts.com/stock/modules/export-data.js"></script>
<script src="/static/external/s3.amazonaws.com/resources.inktankir.com/themes/common/js/linq.min.js"></script>
<script src="/static/external/code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- Custom CSS overrides for Highcharts LTR spacing -->
<style>
/* Target the "Zoom" label */
.highcharts-range-selector-buttons .highcharts-label {
    margin-right: 25px !important; /* Add space before the buttons in LTR */
}

/* Target the "From" and "To" labels next to date inputs */
.highcharts-range-input text {
    margin-right: 2px !important; /* Add small space before the input */
}

/* Ensure date inputs have some breathing room */
.highcharts-range-input input {
    margin-left: 5px !important; /* Add space after the label */
}

/* Hero section styling */
.hero-section {
    position: relative;
    height: 300px;
    overflow: hidden;
    width: 100vw;
    margin-top: 80px;
}
.hero-section img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.5));
}
.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
}
.hero-title {
    color: white;
    font-size: 48px;
    font-weight: 700;
    margin: 0;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
    z-index: 2;
}
.hero-subtitle {
    color: white;
    font-size: 24px;
    font-weight: 400;
    margin: 10px 0 0 0;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
    z-index: 2;
}
.hero-description {
    color: white;
    font-size: 16px;
    font-weight: 300;
    margin: 10px 0 0 0;
    max-width: 600px;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
    z-index: 2;
}

/* Mobile responsive styles for stock information */
@media screen and (max-width: 768px) {
    .hero-section {
        height: 200px !important;
        margin-top: 60px !important;
    }
    .hero-title {
        font-size: 32px !important;
    }
    .hero-subtitle {
        font-size: 18px !important;
        margin-top: 5px !important;
    }
    .hero-description {
        font-size: 14px !important;
        margin-top: 5px !important;
        max-width: 100% !important;
    }
    
    /* Stock information mobile adjustments */
    .stock-info-container {
        flex-direction: column !important;
    }
    .stock-left-panel {
        margin-left: 0 !important;
        padding-left: 20px !important;
        width: 100% !important;
    }
    .stock-right-panel {
        width: 100% !important;
        justify-content: space-between !important;
        padding: 20px !important;
    }
    .stock-price-container {
        font-size: 52px !important;
    }
    .stock-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 15px !important;
        padding: 0 15px !important;
    }
    
    /* Improve value organization */
    .stock-grid > div {
        border-bottom: 1px solid #eee !important;
        padding-bottom: 15px !important;
    }
    
    /* Better organize the stock data panel */
    #msshare-prices h2 {
        font-size: 36px !important;
        padding: 0 15px !important;
    }
    
    /* Improve stock chart container */
    #stock-chart {
        padding: 0 10px !important;
    }
    
    /* Share Information section improvements */
    #msshare-information {
        padding: 0 15px !important;
    }
    
    #msshare-information h2 {
        font-size: 36px !important;
    }
    
    #msshare-information table {
        width: 100% !important;
        display: block !important;
        overflow-x: auto !important;
    }
    
    /* Improve share info panel layout */
    .share-info-panel {
        padding: 30px 0 !important;
    }
    
    .share-info-container {
        padding: 0 15px !important;
    }
    
    .share-info-grid {
        display: grid !important;
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 20px !important;
    }
    
    .info-item {
        border-bottom: 1px solid #eee !important;
        padding-bottom: 15px !important;
    }
    
    /* Investment calculator section */
    #msinvestment-calculator {
        padding: 0 15px !important;
    }
    #msinvestment-calculator h2 {
        font-size: 36px !important;
    }
}

@media screen and (max-width: 480px) {
    .hero-section {
        height: 150px !important;
    }
    .hero-title {
        font-size: 28px !important;
    }
    .hero-subtitle {
        font-size: 16px !important;
        margin-top: 3px !important;
    }
    .hero-description {
        font-size: 12px !important;
        margin-top: 3px !important;
        display: -webkit-box !important;
        -webkit-line-clamp: 2 !important;
        -webkit-box-orient: vertical !important;
        overflow: hidden !important;
    }
    main {
        margin-top: 0 !important;
    }
    
    /* Further adjustments for smallest screens */
    .stock-right-panel {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 15px !important;
    }
    .stock-price-container {
        font-size: 42px !important;
    }
    .change-percentage {
        font-size: 28px !important;
    }
    .stock-grid {
        grid-template-columns: 1fr !important;
    }
    
    /* Improve value organization on smallest screens */
    .stock-grid > div {
        display: flex !important;
        flex-direction: row !important;
        justify-content: space-between !important;
        align-items: center !important;
    }
    
    .stock-grid > div > div:first-child {
        font-size: 10px !important;
    }
    
    /* Adjust headers for tighter spacing */
    #msshare-prices h2, #msinvestment-calculator h2, #msshare-information h2 {
        font-size: 30px !important;
        margin-bottom: 25px !important;
    }
    
    /* Improve chart layout */
    .highcharts-container {
        width: 100% !important;
    }
    
    /* Share information small screen optimizations */
    .share-info-grid {
        grid-template-columns: 1fr !important;
    }
    
    .info-item {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding-bottom: 10px !important;
        margin-bottom: 10px !important;
    }
    
    .info-label, .info-value {
        margin: 0 !important;
        font-size: 14px !important;
    }
    
    .info-label {
        font-weight: 700 !important;
    }
}
</style>

<script type="text/javascript">
    function updateStockValues(data) {
        // Update main values
        $('#last-trade-time').text(data.last_trade_time);
        $('#last-trade-price').text(data.last_trade_price);
        $('#net-last-change').text(data.net_last_change);
        $('#last-change-percentage').text(data.last_change_percentage);
        
        // Update price info
        $('#today-high-price').text(data.today_high_price);
        $('#today-low-price').text(data.today_low_price);
        $('#today-open-price').text(data.today_open_price);
        $('#today-close-price').text(data.today_close_price);
        
        // Update volume and turnover
        $('#today-total-volume').text(data.today_total_volume);
        $('#today-total-value').text(data.today_total_value);
        $('#today-total-trades').text(data.today_total_trades);
        
        // Update 52 week high/low
        $('#year-high-price').text(data.year_high_price);
        $('#year-low-price').text(data.year_low_price);

        // Update change color based on value
        const changeValue = parseFloat(data.net_last_change);
        const changeElement = $('#net-last-change').parent();
        const percentElement = $('#last-change-percentage');
        
        if (changeValue > 0) {
            changeElement.css('background', '#22c55e');
            percentElement.css('color', '#22c55e');
        } else if (changeValue < 0) {
            changeElement.css('background', '#ef4444');
            percentElement.css('color', '#ef4444');
        } else {
            changeElement.css('background', '#666');
            percentElement.css('color', '#666');
        }
    }

    function refreshStockData() {
        $.ajax({
            url: window.location.pathname,
            data: { refresh: 'true' },
            success: function(response) {
                if (response && response.stock_data) {
                    updateStockValues(response.stock_data);
                    
                    // Update chart if it exists
                    if (window.stockChart && response.historical_data) {
                        const chartData = JSON.parse(response.historical_data);
                        window.stockChart.series[0].setData(chartData);
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('Error updating stock data:', error);
            }
        });
    }

    // Refresh every 30 seconds (30000 milliseconds)
    $(document).ready(function() {
        // Add IDs to elements for easier updating
        $('.stock-price').attr('id', 'last-trade-price');
        $('.change-value').attr('id', 'net-last-change');
        $('.change-percentage').attr('id', 'last-change-percentage');
        $('.last-update-time').attr('id', 'last-trade-time');
        $('.today-high').attr('id', 'today-high-price');
        $('.today-low').attr('id', 'today-low-price');
        $('.today-open').attr('id', 'today-open-price');
        $('.today-close').attr('id', 'today-close-price');
        $('.today-volume').attr('id', 'today-total-volume');
        $('.today-turnover').attr('id', 'today-total-value');
        $('.today-trades').attr('id', 'today-total-trades');
        $('.year-high').attr('id', 'year-high-price');
        $('.year-low').attr('id', 'year-low-price');

        // Initial refresh
        refreshStockData();
        
        // Set up periodic refresh
        setInterval(refreshStockData, 30000);
    });
</script>
{% endblock %}

{% block content %}
    <!-- Hero Section - English Content -->
    <div class="hero-section">
        <img src="/static/external/s3.amazonaws.com/resources.inktankir.com/themes/gbautov2/img/slides/bayon2.jpg" alt="Stock and Information">
        <div class="hero-overlay">
            <div class="hero-content" style="direction: ltr; text-align: left; align-items: flex-start;">
                <h1 class="hero-title" lang="en" style="direction: ltr; text-align: left;">Stock and Information</h1>
            </div>
        </div>
    </div>

    <div class="main-section" style="direction: ltr;">
        <div class="container" style="padding: 0;">
            {% if data_source == 'none' %}
                <div class="alert alert-warning">
                    {{ error_message }}
                </div>
            {% else %}
                <!-- Share Prices Section -->
                <div id="msshare-prices" style="margin-bottom: 80px;">
                    
                    <!-- Stock Information Panel -->
                    <div style="background: white; padding: 30px 0; margin: 20px 0;">
                        <div style="max-width: 1200px; margin: 0 auto; padding: 0;">
                            <div class="stock-info-container" style="display: flex; justify-content: space-between; align-items: center;">
                                <!-- Left side with gray background that stretches to the left -->
                                <div class="stock-left-panel" style="background: #f8f8f8; padding: 30px 40px; position: relative; margin-left: -100vw; padding-left: 100vw;">
                                    <div class="last-update-time" style="font-size: 14px; color: #666; margin-bottom: 8px; font-family: 'Helvetica Neue', Arial, sans-serif; font-weight: 400;">{{ last_trade_time }}</div>
                                    <div style="font-size: 64px; font-weight: 200; font-family: 'Helvetica Neue', Arial, sans-serif; letter-spacing: 1px;">1GBCO.CA</div>
                                </div>
                                
                                <!-- Right side -->
                                <div class="stock-right-panel" style="display: flex; align-items: center; gap: 40px; padding-right: 20px;">
                                    <div style="text-align: right;">
                                        <div class="stock-price-container" style="font-size: 72px; font-weight: 600; font-family: 'Helvetica Neue', Arial, sans-serif;"><span class="stock-price">{{ last_trade_price }}</span><span style="font-size: 24px; margin-left: 5px; font-weight: 300;">EGP</span></div>
                                    </div>
                                    <div>
                                        <div style="font-size: 14px; color: #666; margin-bottom: 4px; font-family: 'Helvetica Neue', Arial, sans-serif; font-weight: 400;">Change</div>
                                        <div class="change-value-container {% if net_last_change > 0 %}positive{% elif net_last_change < 0 %}negative{% else %}neutral{% endif %}" style="color: white; padding: 4px 12px; border-radius: 6px; font-size: 20px; font-weight: 400; font-family: 'Helvetica Neue', Arial, sans-serif;"><span class="change-value">{{ net_last_change }}</span></div>
                                    </div>
                                    <div>
                                        <div style="font-size: 14px; color: #666; margin-bottom: 4px; font-family: 'Helvetica Neue', Arial, sans-serif; font-weight: 400;">Change percentage</div>
                                        <div class="change-percentage stock-change-value {% if net_last_change > 0 %}positive{% elif net_last_change < 0 %}negative{% else %}neutral{% endif %}" style="font-size: 36px; font-weight: 400; font-family: 'Helvetica Neue', Arial, sans-serif;">{{ last_change_percentage }}</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Bottom Row -->
                            <div class="stock-grid" style="display: grid; grid-template-columns: repeat(5, 1fr); gap: 20px; margin-top: 40px;">
                                <!-- Day's High -->
                                <div>
                                    <div style="font-size: 12px; color: #000; font-weight: 800; margin-bottom: 8px; font-family: 'Helvetica Neue', Arial, sans-serif; text-transform: uppercase; letter-spacing: 0.5px;">DAY'S HIGH</div>
                                    <div style="font-size: 24px; color: #666; font-weight: 200; font-family: 'Helvetica Neue', Arial, sans-serif;"><span class="today-high">{{ today_high_price }}</span> <span style="font-size: 16px; color: #999; font-weight: 300;">EGP</span></div>
                                </div>

                                <!-- Open -->
                                <div>
                                    <div style="font-size: 12px; color: #000; font-weight: 800; margin-bottom: 8px; font-family: 'Helvetica Neue', Arial, sans-serif; text-transform: uppercase; letter-spacing: 0.5px;">OPEN</div>
                                    <div style="font-size: 24px; color: #666; font-weight: 200; font-family: 'Helvetica Neue', Arial, sans-serif;"><span class="today-open">{{ today_open_price }}</span> <span style="font-size: 16px; color: #999; font-weight: 300;">EGP</span></div>
                                </div>

                                <!-- Volume -->
                                <div>
                                    <div style="font-size: 12px; color: #000; font-weight: 800; margin-bottom: 8px; font-family: 'Helvetica Neue', Arial, sans-serif; text-transform: uppercase; letter-spacing: 0.5px;">VOLUME</div>
                                    <div style="font-size: 24px; color: #666; font-weight: 200; font-family: 'Helvetica Neue', Arial, sans-serif;"><span class="today-volume">{{ today_total_volume }}</span></div>
                                </div>

                                <!-- 52-Week High -->
                                <div>
                                    <div style="font-size: 12px; color: #000; font-weight: 800; margin-bottom: 8px; font-family: 'Helvetica Neue', Arial, sans-serif; text-transform: uppercase; letter-spacing: 0.5px;">52-WEEK HIGH</div>
                                    <div style="font-size: 24px; color: #666; font-weight: 200; font-family: 'Helvetica Neue', Arial, sans-serif;"><span class="year-high" id="year-high-price">{{ year_high_price }}</span> <span style="font-size: 16px; color: #999; font-weight: 300;">EGP</span></div>
                                </div>

                                <!-- Earnings Per Share -->
                                <div>
                                    <div style="font-size: 12px; color: #000; font-weight: 800; margin-bottom: 8px; font-family: 'Helvetica Neue', Arial, sans-serif; text-transform: uppercase; letter-spacing: 0.5px;">EARNINGS PER SHARE</div>
                                    <div style="font-size: 24px; color: #666; font-weight: 200; font-family: 'Helvetica Neue', Arial, sans-serif;">1.68 <span style="font-size: 16px; color: #999; font-weight: 300;">EGP</span></div>
                                    <div style="font-size: 12px; color: #999; font-family: 'Helvetica Neue', Arial, sans-serif; font-weight: 400;">Based on Q4 2023</div>
                                </div>

                                <!-- Day's Low -->
                                <div>
                                    <div style="font-size: 12px; color: #000; font-weight: 800; margin-bottom: 8px; font-family: 'Helvetica Neue', Arial, sans-serif; text-transform: uppercase; letter-spacing: 0.5px;">DAY'S LOW</div>
                                    <div style="font-size: 24px; color: #666; font-weight: 200; font-family: 'Helvetica Neue', Arial, sans-serif;"><span class="today-low">{{ today_low_price }}</span> <span style="font-size: 16px; color: #999; font-weight: 300;">EGP</span></div>
                                </div>

                                <!-- Prev. Close -->
                                <div>
                                    <div style="font-size: 12px; color: #000; font-weight: 800; margin-bottom: 8px; font-family: 'Helvetica Neue', Arial, sans-serif; text-transform: uppercase; letter-spacing: 0.5px;">PREV. CLOSE</div>
                                    <div style="font-size: 24px; color: #666; font-weight: 200; font-family: 'Helvetica Neue', Arial, sans-serif;"><span class="today-close">{{ today_close_price }}</span> <span style="font-size: 16px; color: #999; font-weight: 300;">EGP</span></div>
                                </div>

                                <!-- Turnover -->
                                <div>
                                    <div style="font-size: 12px; color: #000; font-weight: 800; margin-bottom: 8px; font-family: 'Helvetica Neue', Arial, sans-serif; text-transform: uppercase; letter-spacing: 0.5px;">TURNOVER</div>
                                    <div style="font-size: 24px; color: #666; font-weight: 200; font-family: 'Helvetica Neue', Arial, sans-serif;"><span class="today-turnover">{{ today_total_value }}</span> <span style="font-size: 16px; color: #999; font-weight: 300;">EGP</span></div>
                                </div>

                                <!-- 52-Week Low -->
                                <div>
                                    <div style="font-size: 12px; color: #000; font-weight: 800; margin-bottom: 8px; font-family: 'Helvetica Neue', Arial, sans-serif; text-transform: uppercase; letter-spacing: 0.5px;">52-WEEK LOW</div>
                                    <div style="font-size: 24px; color: #666; font-weight: 200; font-family: 'Helvetica Neue', Arial, sans-serif;"><span class="year-low" id="year-low-price">{{ year_low_price }}</span> <span style="font-size: 16px; color: #999; font-weight: 300;">EGP</span></div>
                                </div>

                                <!-- P/E Ratio -->
                                <div>
                                    <div style="font-size: 12px; color: #000; font-weight: 800; margin-bottom: 8px; font-family: 'Helvetica Neue', Arial, sans-serif; text-transform: uppercase; letter-spacing: 0.5px;">P/E RATIO</div>
                                    <div style="font-size: 24px; color: #666; font-weight: 200; font-family: 'Helvetica Neue', Arial, sans-serif;">7.20</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <section class="section section--sm">
                        <div id="stock-chart"></div>
                    </section>
                </div>

                <!-- Share Information Section with improved structure -->
                <div id="msshare-information" class="share-info-section" style="margin-bottom: 80px;">
                  
                    
                    <div class="share-info-container">
                        <!-- Include content with proper classes -->
                        {% include "website/ir.gb-corporation.com/en/share-info.html" %}
                    </div>
                </div>

                <!-- Investment Calculator Section -->
                <div id="msinvestment-calculator" style="margin-bottom: 80px;">
                    {% include "website/ir.gb-corporation.com/en/investment-calculator.html" %}
                </div>
            {% endif %}
        </div>
    </div>

<script type="text/javascript">
document.addEventListener('DOMContentLoaded', function() {
    var stockChart = $('#stock-chart');
    if (stockChart.length && '{{ data_source }}' !== 'none') {
        // Set language options for range selector labels
        Highcharts.setOptions({
            lang: {
                rangeSelectorFrom: 'From',
                rangeSelectorTo: 'To'
            }
        });

        fetch('/api/stock-history/?chart=true')
            .then(response => response.json())
            .then(data => {
                // Transform data for Highcharts
                const ohlc = [];
                const volumes = [];
                
                // Filter out any invalid data points
                data.forEach(item => {
                    const timestamp = new Date(item.date).getTime();
                    const closePrice = parseFloat(item.close_price);
                    
                    // Only add valid data points
                    if (!isNaN(timestamp) && !isNaN(closePrice) && closePrice !== null) {
                        ohlc.push([
                            timestamp,
                            closePrice
                        ]);
                        
                        volumes.push([
                            timestamp,
                            parseFloat(item.volume)
                        ]);
                    }
                });

                // Sort data by date
                ohlc.sort((a, b) => a[0] - b[0]);
                volumes.sort((a, b) => a[0] - b[0]);

                // Create the chart with existing styling
                Highcharts.stockChart('stock-chart', {
                    chart: {
                        zoomType: 'x',
                        panning: {
                            enabled: true,
                            type: 'x'
                        },
                        panKey: 'shift'
                    },

                    rangeSelector: {
                        selected: 1,
                        inputEnabled: true,
                        inputDateFormat: '%b %e, %Y',
                        inputBoxBorderColor: 'transparent',
                        inputBoxStyle: {
                           backgroundColor: 'transparent'
                        },
                        inputStyle: {
                           color: '#666666',
                           fontWeight: 'normal',
                           fontSize: '14px'
                        },
                        labelStyle: {
                           color: '#666666',
                           fontWeight: 'normal',
                           fontSize: '14px'
                        },
                        buttonSpacing: 5,
                        buttonTheme: {
                            fill: 'none',
                            stroke: 'none',
                            style: {
                                fontSize: '14px'
                            },
                            states: {
                                hover: {
                                    fill: '#f0f0f0',
                                    stroke: 'none'
                                },
                                select: {
                                    fill: 'none',
                                    stroke: 'none',
                                    style: {
                                        fontWeight: 'bold'
                                    }
                                }
                            }
                        },
                        buttons: [{
                            type: 'month',
                            count: 1,
                            text: '1m'
                        }, {
                            type: 'month',
                            count: 3,
                            text: '3m'
                        }, {
                            type: 'month',
                            count: 6,
                            text: '6m'
                        }, {
                            type: 'ytd',
                            text: 'YTD'
                        }, {
                            type: 'year',
                            count: 1,
                            text: '1y'
                        }, {
                            type: 'all',
                            text: 'All'
                        }]
                    },

                 

                    credits: {
                        enabled: false
                    },

                    exporting: {
                        enabled: false
                    },

                    colors: ["#3366CC", "#3366CC", "#3366CC", "#3366CC", "B3B3B3"],
                    
                    plotOptions: {
                        areaspline: {
                            lineSmooth: true,
                            smoothing: 1.5,
                            crisp: false,
                            animation: {
                                duration: 2000
                            }
                        },
                        series: {
                            allowPointSelect: true,
                            cursor: 'crosshair'
                        }
                    },

                    navigator: {
                        height: 50,
                        margin: 10,
                        series: {
                            color: '#3366CC',
                            fillOpacity: 0.05,
                            lineWidth: 2
                        }
                    },

                    scrollbar: {
                        enabled: true,
                        height: 20
                    },

                    xAxis: {
                        type: 'datetime',
                        ordinal: true,
                        gridLineWidth: 0,
                        lineColor: '#C0C0C0',
                        tickColor: '#C0C0C0',
                        labels: {
                            style: {
                                color: '#666666'
                            }
                        },
                        events: {
                            afterSetExtremes: function(e) {
                                // Enable zooming behavior
                                if (e.trigger !== 'syncExtremes') {
                                    this.chart.showResetZoom();
                                }
                            }
                        }
                    },

                    yAxis: {
                        opposite: true,
                        gridLineColor: '#EEEEEE',
                        labels: {
                            align: 'left',
                            x: 10,
                            style: {
                                color: '#666666'
                            }
                        },
                        title: {
                            text: null
                        },
                        plotLines: [{
                            value: 0,
                            width: 1,
                            color: '#C0C0C0'
                        }]
                    },

                    series: [{
                        name: 'GB Auto Stock Price',
                        data: ohlc,
                        type: 'line',
                        threshold: null,
                        lineWidth: 2,
                        connectNulls: true,
                        gapSize: 0,
                        connectEnds: true,
                        states: {
                            hover: {
                                lineWidth: 2
                            }
                        },
                        tooltip: {
                            valueDecimals: 2
                        },
                        color: '#3366CC',
                        marker: {
                            enabled: false,
                            radius: 2,
                            states: {
                                hover: {
                                    enabled: true,
                                    radius: 4
                                }
                            }
                        },
                        shadow: false,
                        animation: {
                            duration: 1500
                        }
                    }]
                });
            })
            .catch(error => {
                console.error('Error loading stock history:', error);
                stockChart.html('<div class="alert alert-warning">Unable to load stock chart data at this time.</div>');
            });
    }
});
</script>

<!-- Add margin space -->
<div style="margin-bottom: 60px;"></div>

<style>
.footer, footer {
    background: #ffffff !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    box-shadow: none !important;
}

.change-value-container.positive {
    background-color: #22c55e;
}
.change-value-container.negative {
    background-color: #ef4444;
}
.change-value-container.neutral {
    background-color: #666;
}
.stock-change-value.positive {
    color: #22c55e;
}
.stock-change-value.negative {
    color: #ef4444;
}
.stock-change-value.neutral {
    color: #666;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Function to ensure English hero content
    document.addEventListener('DOMContentLoaded', function() {
        // Hide all hero sections except the first one
        var heroSections = document.querySelectorAll('.hero-section');
        if (heroSections.length > 1) {
            for (var i = 1; i < heroSections.length; i++) {
                heroSections[i].style.display = 'none';
            }
        }
        
        // Ensure hero content is in English
        var heroTitles = document.querySelectorAll('.hero-title');
        if (heroTitles.length > 0) {
            // Set first title to English
            heroTitles[0].setAttribute('lang', 'en');
            heroTitles[0].textContent = "Stock and Information";
            heroTitles[0].style.textAlign = 'left';
            heroTitles[0].style.direction = 'ltr';
            
            // Hide any additional hero titles
            for (var i = 1; i < heroTitles.length; i++) {
                heroTitles[i].style.display = 'none';
            }
        }
        
        // Ensure hero content alignment is English (LTR)
        var heroContent = document.querySelectorAll('.hero-content');
        if (heroContent.length > 0) {
            heroContent[0].style.direction = 'ltr';
            heroContent[0].style.textAlign = 'left';
            heroContent[0].style.alignItems = 'flex-start';
        }
        
        // Force main content to be LTR for English page
        var mainSection = document.querySelector('.main-section');
        if (mainSection) {
            mainSection.style.direction = 'ltr';
        }
    });
</script>
{% endblock %}