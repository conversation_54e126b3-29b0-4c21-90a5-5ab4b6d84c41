"""
Security decorators for function-based views
"""

import json
import logging
from functools import wraps
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from api.tools import get_client_ip

logger = logging.getLogger(__name__)


def apply_throttling(throttle_class):
    """
    Decorator to apply throttling to function-based views
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Create throttle instance
            throttle = throttle_class()
            
            # Check if request is allowed
            if not throttle.allow_request(request, None):
                return JsonResponse({
                    'success': False,
                    'status': 'error',
                    'message': 'Too many requests. Please try again later.'
                }, status=429)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


def enhanced_security(throttle_class=None, require_post=True, validate_email=True):
    """
    Enhanced security decorator with comprehensive protection
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            try:
                # Import security modules
                from core.email_security import email_security_validator
                from core.subscription_security import subscription_abuse_detector
                from core.security_monitoring import security_monitor, SecurityEventType
                from core.honeypot_protection import honeypot_protection
                
                # Get client information
                ip_address = get_client_ip(request)
                user_agent = request.META.get('HTTP_USER_AGENT', '')
                
                # Apply throttling if specified
                if throttle_class:
                    throttle = throttle_class()
                    if not throttle.allow_request(request, None):
                        return JsonResponse({
                            'success': False,
                            'status': 'error',
                            'message': 'Too many requests. Please try again later.'
                        }, status=429)
                
                # Check HTTP method
                if require_post and request.method != 'POST':
                    return JsonResponse({
                        'success': False,
                        'status': 'error',
                        'message': 'Only POST method is allowed'
                    }, status=405)
                
                # Parse request data
                if request.method == 'POST':
                    try:
                        if request.content_type == 'application/json':
                            data = json.loads(request.body)
                        else:
                            data = request.POST.dict()
                    except (json.JSONDecodeError, ValueError):
                        return JsonResponse({
                            'success': False,
                            'status': 'error',
                            'message': 'Invalid request data'
                        }, status=400)
                    
                    email = data.get('email', '').strip()
                    
                    # Validate email if required
                    if validate_email and email:
                        # Email security validation
                        email_validation = email_security_validator.validate_email_security(email, ip_address)
                        if not email_validation['is_valid']:
                            security_monitor.log_security_event(
                                SecurityEventType.INVALID_EMAIL,
                                email,
                                ip_address,
                                {
                                    'user_agent': user_agent,
                                    'validation_details': email_validation,
                                    'endpoint': request.path
                                },
                                'medium'
                            )
                            return JsonResponse({
                                'success': False,
                                'status': 'error',
                                'message': 'Invalid email address'
                            }, status=400)
                        
                        # Abuse detection
                        abuse_check = subscription_abuse_detector.check_subscription_abuse(email, ip_address, user_agent)
                        if abuse_check['block_submission']:
                            security_monitor.log_security_event(
                                SecurityEventType.ABUSE_DETECTED,
                                email,
                                ip_address,
                                {
                                    'user_agent': user_agent,
                                    'abuse_details': abuse_check,
                                    'endpoint': request.path
                                },
                                'high'
                            )
                            return JsonResponse({
                                'success': False,
                                'status': 'error',
                                'message': 'Suspicious activity detected'
                            }, status=403)
                        
                        # Honeypot protection
                        honeypot_check = honeypot_protection.validate_honeypot_submission(data, ip_address)
                        if honeypot_check['block_submission']:
                            security_monitor.log_security_event(
                                SecurityEventType.BOT_DETECTED,
                                email,
                                ip_address,
                                {
                                    'user_agent': user_agent,
                                    'honeypot_details': honeypot_check,
                                    'endpoint': request.path
                                },
                                'high'
                            )
                            return JsonResponse({
                                'success': False,
                                'status': 'error',
                                'message': 'Bot detected'
                            }, status=403)
                
                # Call the original view function
                return view_func(request, *args, **kwargs)
                
            except Exception as e:
                logger.error(f"Security decorator error in {view_func.__name__}: {str(e)}")
                return JsonResponse({
                    'success': False,
                    'status': 'error',
                    'message': 'An error occurred'
                }, status=500)
        
        return wrapper
    return decorator


def basic_security(throttle_class=None, require_post=True):
    """
    Basic security decorator with throttling and method validation
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            try:
                # Apply throttling if specified
                if throttle_class:
                    throttle = throttle_class()
                    if not throttle.allow_request(request, None):
                        return JsonResponse({
                            'success': False,
                            'status': 'error',
                            'message': 'Too many requests. Please try again later.'
                        }, status=429)
                
                # Check HTTP method
                if require_post and request.method != 'POST':
                    return JsonResponse({
                        'success': False,
                        'status': 'error',
                        'message': 'Only POST method is allowed'
                    }, status=405)
                
                # Call the original view function
                return view_func(request, *args, **kwargs)
                
            except Exception as e:
                logger.error(f"Basic security decorator error in {view_func.__name__}: {str(e)}")
                return JsonResponse({
                    'success': False,
                    'status': 'error',
                    'message': 'An error occurred'
                }, status=500)
        
        return wrapper
    return decorator
